import {
  createApi,
  fetchBaseQ<PERSON>y,
  FetchBaseQueryError,
  BaseQueryFn,
} from '@reduxjs/toolkit/query/react'; // Removed Api import
import type { GptModel } from '@/lib/types/common'; // Import GptModel from common types
import { RootState } from './store'; // Import RootState to access state in queryFn
import { getSession } from 'next-auth/react'; // Import getSession
import {
  normalizeModelName,
  getDisplayNameFromModelName,
} from '@/lib/utils/modelNameNormalization'; // Import for display-only normalization
// Import necessary actions from chatSlice
import {
  updateLastMessageContent,
  setIsThinking,
  setConversationId,
  setChatErrorMessage,
  addUserMessage,
  removeLastMessage, // Import removeLastMessage for error handling
  addSourcesToLastMessage, // Import the new action
  updateLastMessageModelDisplayName, // Import the new action
  updateLastMessageStreamingStatus, // Import the new action for streaming status
  Conversation, // Import Conversation type
  Source, // Import Source type
  setConversationParameters, // Import the new action
  selectSelectedNextModelName, // Import selector for multi-LLM
  selectConversationModel, // Import selector for conversation model
  setSelectedNextModelName, // Import action for multi-LLM
  setConversationModel, // Import action for conversation model
  setStreamJustCompleted, // Import action for stream completion tracking
} from './chatSlice';
// Import selector for available models
import { selectAvailableModels } from './modelSlice';

// --- Type Definitions ---


// Define the structure of the message coming from the backend API for history
// This should match the HistoryMessageDto in the backend service
interface BackendMessage {
  message_uuid: string;
  sender: 'user' | 'assistant'; // Assuming sender matches role
  create_dt: string; // ISO Date string
  model_name: string | null;
  reaction: string | null;
  token_spent: number | null;
  prompt_order: number;
  content: string;
  // conversation_title: string | null; // Title is now part of the wrapper object, not each message
  sources?: Source[] | null; // Added sources field matching ChatService DTO
  used_mention?: boolean | null; // Whether user explicitly used @mention to select the model
}

// Expected raw response from the /api/general/model_list endpoint
// Removed unused RawModelsResponse interface


// Request body for the general chat completion endpoint /api/general/chat/completions
// Should align with CreateChatCompletionDto in the backend's chat-completion.controller
export interface ChatCompletionRequest {
  chat_session_id?: string; // Optional: Existing chat session ID (Renamed from conversation_uuid)
  prompt: string; // The user prompt (Replaced messages array)
  model: string; // ID of the model to use
  temperature?: number;
  stream?: boolean; // Crucial: Set to true for streaming
  useGoogle?: boolean; // Specific flag if needed
  files?: { filename: string; mimeType: string; content: string }[]; // Added files property
  instructions?: string;
  isRegeneration?: boolean; // Flag to indicate this is a regeneration request
  usedMention?: boolean; // Flag to indicate whether the user explicitly used @mention to select the model
  // Add other parameters matching CreateChatCompletionDto as needed
}

// Response type for non-streaming general chat completion
// (Not used if streaming, but good for reference)
export interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  conversation_uuid: string;
  choices: [
    {
      index: 0;
      message: {
        role: 'assistant';
        content: string;
      };
      finish_reason: string;
    },
  ];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Type for standard content SSE data chunks
interface ContentChunk {
  id: string;
  object: 'chat.completion.chunk'; // Literal type
  created: number;
  model: string;
  model_display_name?: string;
  conversation_uuid?: string;
  choices: [
    {
      index: 0;
      delta: { content?: string };
      finish_reason: string | null;
    },
  ];
  // Add other potential fields like usage if needed
}

// Type for the custom sources SSE data chunk
interface SourceChunk {
  type: 'sources'; // Literal type
  sources: Source[];
  id?: string; // Optional common fields
  conversation_uuid?: string;
}

// Union type for any valid chunk from the stream
type StreamChunk = ContentChunk | SourceChunk;

// Request body for updating like status
export interface UpdateMessageLikeRequest {
  messageId: string;
  liked: boolean | undefined;
}

// Request body for getting API Key
export interface ApiKeyRequestBody {
  deploymentName: string;
}

// Response body for getting API Key
export interface ApiKeyResponseBody {
  api_key: string;
}

// Request body for health check
export interface HealthCheckRequest {
  message: string;
  model: string;
  temperature?: number;
}

// Type for grouped conversation history (dynamic keys)
export interface GroupedHistoryResponse {
  [key: string]: { id: string | null; title: string; model: string | null }[]; // Added model field
}

// --- New Types for Paginated Conversation History ---
export interface ConversationHistoryItem {
  id: string;
  title: string;
  model: string | null;
  updated_at: string; // ISO Date string
}

export interface PaginatedConversationHistoryResponse {
  items: ConversationHistoryItem[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
}

// Type for the parameters part of the history response
interface ConversationParamsFromHistory {
  instructions?: string | null;
  pastMessagesCount?: number | null;
  maxResponseTokens?: number | null;
  temperature?: number | null;
  topP?: number | null;
}

// Type for the request body of the update mutation
// This should mirror UpdateConversationParamsDto from the backend
interface UpdateConversationParamsRequest {
  chat_session_id: string;
  instructions?: string | null;
  pastMessagesCount?: number | null;
  maxResponseTokens?: number | null;
  temperature?: number | null;
  topP?: number | null;
}

// Updated response type for getHistoryMessages query
interface HistoryMessagesResponse {
  messages: Conversation[];
  title: string | null;
  parameters: ConversationParamsFromHistory | null; // Add parameters
}

export interface RewritePromptRequest {
  prompt: string;
}

export interface GeneratePromptRequest {
  idea: string;
}

export interface PromptRewriteResponse {
  prompt: string;
}

export interface FeedbackRequest {
  subject?: string;
  content: string;
  email?: string;
}

export interface FeedbackResponse {
  message: string;
  feedbackId?: number;
}

// --- API Slice Definition ---

// Read the base URL from the environment variable
const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
console.log(`API Base URL for RTK Query: ${apiBaseUrl}`); // Log for verification

const baseQuery = fetchBaseQuery({
  baseUrl: apiBaseUrl || '/api/v0',
  prepareHeaders: async (headers) => {
    // Make prepareHeaders async
    const session = await getSession(); // Get session directly
    console.log(
      '[DEBUG] prepareHeaders - Session:',
      session ? 'Present' : 'Not present',
    ); // Log session presence
    console.log(
      '[DEBUG] session  accessToken:',
      session?.accessToken
        ? `Present (length: ${session.accessToken.length})`
        : 'Not present',
    ); // Log accessToken presence and length
    const token = session?.accessToken; // Access accessToken from session
    // console.log('[DEBUG] prepareHeaders - Token from session:', token ? `Present (length: ${token.length})` : 'Not present'); // General log
    if (token) {
      headers.set('authorization', `Bearer ${token}`);
      console.log('[DEBUG] prepareHeaders - Authorization header SET'); // General log
    } else {
      console.log(
        '[DEBUG] prepareHeaders - No token found, Authorization header NOT SET',
      ); // General log
    }
    return headers;
  },
});

// Define TagTypes explicitly for clarity and typing
type TagTypes =
  | 'Models'
  | 'Usage'
  | 'ApiKey'
  | 'HealthCheck'
  | 'RecentModels'
  | 'History'
  | 'ChatMessages'
  | 'PromptGallery'
  | 'Tasks';

export const apiSlice = createApi({
  // Removed explicit type annotation
  reducerPath: 'api',
  baseQuery: baseQuery, // Reverted to the simpler baseQuery
  tagTypes: [
    'Models',
    'Usage',
    'ApiKey',
    'HealthCheck',
    'RecentModels',
    'History',
    'ChatMessages',
    'PromptGallery',
    'Tasks',
  ], // Removed cast
  endpoints: (builder) => ({
    // Fetches the list of available models
    getModels: builder.query<GptModel[], void>({
      query: () => ({
        url: 'general/model_list', // Removed timestamp
        method: 'GET',
      }),
      transformResponse: (response: { model_list: GptModel[] }) => {
        const models = response?.model_list || [];
        // Apply display-only normalization to display_name while keeping model_name original
        return models.map((model) => ({
          ...model,
          display_name:
            normalizeModelName(model.display_name) || model.display_name,
        }));
      },
      providesTags: ['Models'],
    }),

    // Fetches recently used models
    getRecentModels: builder.query<GptModel[], { limit?: number }>({
      // Use GptModel[] as return type
      query: ({ limit }) => ({
        url: `general/recent-models?limit=${limit || 4}`,
        method: 'GET',
      }), // Updated path
      transformResponse: (response: GptModel[]) => {
        // Apply display-only normalization to display_name while keeping model_name original
        return response.map((model) => ({
          ...model,
          display_name:
            normalizeModelName(model.display_name) || model.display_name,
        }));
      },
      providesTags: ['RecentModels'],
    }),

    // Fetches conversation history (now paginated)
    getConversationHistory: builder.query<
      PaginatedConversationHistoryResponse,
      { page: number; limit: number }
    >({
      query: ({ page, limit }) => ({
        url: `general/conversations/history?page=${page}&limit=${limit}&sort_by=updated_at&order=desc`,
        method: 'GET',
      }),
      providesTags: (result) =>
        result?.items
          ? [
              ...result.items.map(({ id }) => ({
                type: 'History' as const,
                id,
              })),
              { type: 'History', id: 'LIST' },
            ]
          : [{ type: 'History', id: 'LIST' }],
      // transformResponse: (response: any) => { /* Adjust if backend doesn't match PaginatedConversationHistoryResponse directly */ }
    }),

    getAllConversationHistory: builder.query<ConversationHistoryItem[], void>({
      query: () => ({
        url: 'general/chat/history/all',
        method: 'GET',
      }),
      transformResponse: (response: { items: ConversationHistoryItem[] }) => {
        return response.items;
      },
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({
                type: 'History' as const,
                id,
              })),
              { type: 'History', id: 'LIST' },
            ]
          : [{ type: 'History', id: 'LIST' }],
    }),

    // Fetches messages and title for a specific conversation
    // Fetches messages, title, and parameters for a specific conversation
    getHistoryMessages: builder.query<HistoryMessagesResponse, string>({
      // Updated return type
      queryFn: async (
        chatSessionId,
        { signal, dispatch, getState },
        _extraOptions,
        baseQuery,
      ) => {
        // Get available models from Redux state for display name mapping
        const state = getState() as RootState;
        const availableModels = selectAvailableModels(state);

        // Make the API call
        const result = await baseQuery({
          url: `general/chat/history_messages?chat_session_id=${chatSessionId}`,
          method: 'GET',
        });

        if (result.error) {
          return { error: result.error };
        }

        // Transform the response with access to available models
        const response = result.data as {
          messages: BackendMessage[];
          title: string | null;
          parameters: ConversationParamsFromHistory | null;
        };

        const title = response.title;
        const parameters = response.parameters;

        const formattedMessages: Conversation[] = response.messages.map(
          (msg) => {
            let content = msg.content;
            let attachments: Array<{ name: string; type: string; size: number }> | undefined;

            // Check if the content contains the file indicator
            const fileMatch = content.match(/ \[Files: (.*)\]$/);
            if (fileMatch) {
              content = content.replace(fileMatch[0], ''); // Remove the file indicator from the content
              attachments = fileMatch[1].split(', ').map(name => ({ name, type: 'unknown', size: 0 }));
            }

            // Enhanced model display name resolution with better error handling
            let modelDisplayName: string | undefined = undefined;
            if (msg.model_name) {
              try {
                // Try to get display name from available models
                const resolvedDisplayName = getDisplayNameFromModelName(msg.model_name, availableModels);
                
                if (resolvedDisplayName) {
                  modelDisplayName = resolvedDisplayName;
                  console.log(`[getHistoryMessages] ✅ Model display name resolved:`, {
                    messageId: msg.message_uuid,
                    originalModelName: msg.model_name,
                    resolvedDisplayName,
                    availableModelsCount: availableModels?.length || 0,
                  });
                } else {
                  // If no display name could be resolved, log for debugging
                  console.warn(`[getHistoryMessages] ⚠️ Could not resolve display name:`, {
                    messageId: msg.message_uuid,
                    originalModelName: msg.model_name,
                    availableModelsCount: availableModels?.length || 0,
                    availableModelNames: availableModels?.map(m => m.model_name) || [],
                  });
                  // Still use the original model name as fallback
                  modelDisplayName = msg.model_name;
                }
              } catch (error) {
                console.error(`[getHistoryMessages] 🚨 Error resolving model display name:`, {
                  messageId: msg.message_uuid,
                  originalModelName: msg.model_name,
                  error,
                });
                // Fallback to original model name on error
                modelDisplayName = msg.model_name;
              }
            }

            return {
              role: msg.sender,
              content: content,
              messageId: msg.message_uuid,
              timestamp: new Date(msg.create_dt).getTime(),
              model_display_name: modelDisplayName,
              model_api_name: msg.model_name || undefined, // Preserve original API name for backend calls
              like:
                msg.reaction === 'L'
                  ? true
                  : msg.reaction === 'D'
                    ? false
                    : undefined,
              sources: msg.sources ?? undefined,
              attachments: attachments, // Add the parsed attachments
              usedMention: msg.used_mention ?? false, // Map used_mention to usedMention field
            };
          },
        );

        // SAFEGUARD: Check for incomplete conversations
        const userMessages = formattedMessages.filter((m) => m.role === 'user');
        const assistantMessages = formattedMessages.filter(
          (m) => m.role === 'assistant',
        );

        if (userMessages.length > 0 && assistantMessages.length === 0) {
          console.warn(
            `[apiSlice] ⚠️ Incomplete conversation detected for ${chatSessionId}: ${userMessages.length} user messages, ${assistantMessages.length} assistant messages. Assistant response may still be processing.`,
          );
        }

        const messagesWithSources = assistantMessages.filter(
          (m) => m.sources && m.sources.length > 0,
        );
        console.log(
          `[apiSlice] 📊 Conversation ${chatSessionId}: ${formattedMessages.length} total messages, ${messagesWithSources.length} assistant messages with sources`,
        );

        // Return the transformed data
        return {
          data: {
            messages: formattedMessages,
            title: title,
            parameters: parameters,
          },
        };
      },
      // Dispatch action to set parameters and conversation model in chatSlice after successful fetch
      async onQueryStarted(
        chatSessionId,
        { dispatch, queryFulfilled, getState },
      ) {
        try {
          const { data } = await queryFulfilled;
          if (data.parameters) {
            dispatch(setConversationParameters(data.parameters));
          }
          // Set conversation model from the FIRST assistant message (chronologically)
          if (data.messages && data.messages.length > 0) {
            const firstAssistantMessage = data.messages
              .find((msg) => msg.role === 'assistant' && msg.model_api_name);
            if (firstAssistantMessage?.model_api_name) {
              // Always set the conversation model to the first model used
              dispatch(
                setConversationModel(firstAssistantMessage.model_api_name),
              );

              // Don't automatically set selectedNextModelName for existing conversations
              // Let users explicitly choose via @mention or model selector
              console.log(
                `[apiSlice] Set conversation model to first used model: ${firstAssistantMessage.model_api_name}`,
              );
            }
          }
        } catch (err) {
          // Handle error if needed
          console.error('Failed to fetch or set conversation parameters:', err);
        }
      },
      providesTags: (result, error, chatSessionId) =>
        result?.messages ? [{ type: 'ChatMessages', id: chatSessionId }] : [],
    }),

    // --- Define endpoints EXCEPT chatCompletion initially ---

    // Endpoint for updating like status
    updateMessageLike: builder.mutation<void, UpdateMessageLikeRequest>({
      query: ({ messageId, liked }) => {
        const reaction = liked === undefined ? -1 : liked ? 1 : 0; // Assuming -1 for clear, 1 for like, 0 for dislike
        return {
          url: 'general/reaction',
          method: 'POST',
          body: { messageId, reaction },
        };
      },
      invalidatesTags: ['ChatMessages'],
    }),

    // Endpoint for getting API Key
    getApiKey: builder.mutation<ApiKeyResponseBody, ApiKeyRequestBody>({
      query: (body) => ({ url: 'general/api-key', method: 'POST', body: body }), // Corrected URL path
      invalidatesTags: ['ApiKey'],
    }),

    // Endpoint for health check (can remain as is or be removed if not used)
    submitHealthCheckPrompt: builder.mutation<string, HealthCheckRequest>({
      async queryFn(
        _args,
        {
          /* signal, dispatch, getState */
        },
        _extraOptions,
        _baseQuery,
      ) {
        // Prefixed unused parameters
        // ... (health check implementation using fetch or baseQuery) ...
        console.warn('submitHealthCheckPrompt endpoint called');
        return { data: 'Health check simulation complete.' };
      },
      invalidatesTags: ['HealthCheck'],
    }),

    // --- New Mutation for Updating Conversation Parameters ---
    updateConversationParams: builder.mutation<
      void,
      UpdateConversationParamsRequest
    >({
      query: (body) => ({
        url: 'general/chat/parameters', // Matches the backend endpoint
        method: 'PATCH',
        body: body,
      }),
      // Optional: Optimistic update or invalidation if needed
      async onQueryStarted(
        { chat_session_id, ...patch },
        { dispatch, queryFulfilled },
      ) {
        // Optimistically update the local state
        dispatch(setConversationParameters(patch));
        try {
          await queryFulfilled;
        } catch {
          // TODO: Consider reverting the optimistic update on failure
          // dispatch(chatSlice.util.patchQueryData('getHistoryMessages', chat_session_id, (draft) => { ...revert logic... }));
          console.error('Failed to update conversation parameters');
        }
      },
      // Invalidate history messages to refetch potentially updated parameters?
      // invalidatesTags: (result, error, { chat_session_id }) => [{ type: 'ChatMessages', id: chat_session_id }],
    }),

    // Share conversation endpoint
    shareConversation: builder.mutation<
      { shareId: string; shareUrl: string },
      string // conversationId
    >({
      query: (conversationId) => ({
        url: `general/chat/share/${conversationId}`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, conversationId) => [
        { type: 'History', id: conversationId },
      ],
    }),

    // Access shared conversation endpoint
    accessSharedConversation: builder.mutation<
      { conversationUuid: string; redirectUrl: string },
      string // shareId
    >({
      query: (shareId) => ({
        url: `general/chat/shared/${shareId}`,
        method: 'GET',
      }),
      invalidatesTags: ['History'], // Invalidate history list as new conversation was created
    }),

    // TODO: Add endpoints for Feedback, T&C (GET/POST), Notify (GET/PUT), OCR
    // TODO: Add mutation for recording recent model usage (POST /recent-models/usage) if needed explicitly
    renameConversation: builder.mutation<
      void,
      { conversationId: string; newTitle: string }
    >({
      query: ({ conversationId, newTitle }) => ({
        url: `general/chat/${conversationId}/title`,
        method: 'PATCH',
        body: { newTitle },
      }),
      async onQueryStarted(
        { conversationId, newTitle },
        { dispatch, queryFulfilled },
      ) {
        const allHistoryPatchResult = dispatch(
          apiSlice.util.updateQueryData(
            'getAllConversationHistory',
            undefined,
            (draft) => {
              const item = draft.find((item) => item.id === conversationId);
              if (item) {
                item.title = newTitle;
              }
            },
          ),
        );
        const paginatedHistoryPatchResult = dispatch(
          apiSlice.util.updateQueryData(
            'getConversationHistory',
            { page: 1, limit: 10 }, // Adjust if you have variables
            (draft) => {
              const item = draft.items.find(
                (item) => item.id === conversationId,
              );
              if (item) {
                item.title = newTitle;
              }
            },
          ),
        );
        const singleHistoryPatchResult = dispatch(
          apiSlice.util.updateQueryData(
            'getHistoryMessages',
            conversationId,
            (draft) => {
              draft.title = newTitle;
            },
          ),
        );
        try {
          await queryFulfilled;
        } catch {
          allHistoryPatchResult.undo();
          paginatedHistoryPatchResult.undo();
          singleHistoryPatchResult.undo();
        }
      },
    }),
    deleteConversation: builder.mutation<void, string>({
      query: (conversationId) => ({
        url: `general/chat/${conversationId}`,
        method: 'DELETE',
      }),
      async onQueryStarted(conversationId, { dispatch, queryFulfilled }) {
        // Optimistically update the 'getAllConversationHistory' cache
        const allHistoryPatchResult = dispatch(
          apiSlice.util.updateQueryData(
            'getAllConversationHistory',
            undefined,
            (draft) => {
              const index = draft.findIndex(
                (item) => item.id === conversationId,
              );
              if (index !== -1) {
                draft.splice(index, 1);
              }
            },
          ),
        );
        // Optimistically update the 'getConversationHistory' paginated cache
        const paginatedHistoryPatchResult = dispatch(
          apiSlice.util.updateQueryData(
            'getConversationHistory',
            { page: 1, limit: 10 }, // Adjust if you have variables
            (draft) => {
              const index = draft.items.findIndex(
                (item) => item.id === conversationId,
              );
              if (index !== -1) {
                draft.items.splice(index, 1);
              }
            },
          ),
        );
        try {
          await queryFulfilled;
        } catch {
          allHistoryPatchResult.undo();
          paginatedHistoryPatchResult.undo();
        }
      },
    }),
    getTasks: builder.query<any[], void>({
      query: () => 'general/tasks',
      providesTags: ['Tasks'],
    }),
    getPromptGallery: builder.query<any[], void>({
      query: () => 'general/prompt-gallery',
      providesTags: ['PromptGallery'],
    }),
    createPromptGallery: builder.mutation<any, any>({
      query: (body) => ({
        url: 'general/prompt-gallery',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['PromptGallery'],
    }),
    updatePromptGallery: builder.mutation<any, any>({
      query: ({ id, ...body }) => ({
        url: `general/prompt-gallery/${id}`,
        method: 'PATCH',
        body,
      }),
      invalidatesTags: ['PromptGallery'],
    }),
    deletePromptGallery: builder.mutation<any, number>({
      query: (id) => ({
        url: `general/prompt-gallery/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['PromptGallery'],
    }),
    rewritePrompt: builder.mutation<PromptRewriteResponse, RewritePromptRequest>({
      query: (body) => ({
        url: 'general/prompt-rewrite/rewrite',
        method: 'POST',
        body,
      }),
    }),
    generatePrompt: builder.mutation<PromptRewriteResponse, GeneratePromptRequest>({
      query: (body) => ({
        url: 'general/prompt-rewrite/generate',
        method: 'POST',
        body,
      }),
    }),
    submitFeedback: builder.mutation<FeedbackResponse, FeedbackRequest>({
      query: (body) => ({
        url: 'general/feedback',
        method: 'POST',
        body,
      }),
    }),
  }),
});

// --- Inject chatCompletion endpoint to break circular dependency ---
const extendedApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    chatCompletion: builder.mutation<
      { success: boolean },
      ChatCompletionRequest
    >({
      queryFn: async (
        args,
        { signal, dispatch, getState },
        _extraOptions,
        _baseQuery,
      ) => {
        // --- Multi-LLM Logic ---
        const state = getState() as RootState;
        const selectedNextModel = selectSelectedNextModelName(state);
        
        // Only override model selection when user explicitly used @mention
        const modelToSend = args.usedMention 
          ? (selectedNextModel ?? args.model)
          : args.model;
        
        console.log(
          `[apiSlice] Model selection: usedMention=${args.usedMention}, selectedNextModel=${selectedNextModel}, originalModel=${args.model}, modelToSend=${modelToSend}`,
        );
        // --- End Multi-LLM Logic ---

        // Construct request body
        const baseRequestBody = {
          prompt: args.prompt,
          model: modelToSend,
          temperature: args.temperature,
          useGoogle: args.useGoogle,
          stream: args.stream !== false, // Ensure stream is true unless explicitly false
          instructions: args.instructions,
          isRegeneration: args.isRegeneration, // Pass regeneration flag to backend
          usedMention: args.usedMention, // Pass @mention usage flag to backend
        };
        const uuidRegex =
          /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
        const isValidUuid =
          typeof args.chat_session_id === 'string' &&
          uuidRegex.test(args.chat_session_id);
        const requestBody = isValidUuid
          ? {
              ...baseRequestBody,
              chat_session_id: args.chat_session_id,
              files: args.files,
            }
          : { ...baseRequestBody, files: args.files };

        // Log the prepared request body for debugging, excluding file content
        const loggableBody = { ...requestBody };
        if (loggableBody.files) {
          loggableBody.files = loggableBody.files.map((file: any) => ({
            ...file,
            content: `[${file.content.length} chars]`,
          }));
        }
        /*
       console.log('🚀 API Request Prepared:');
       console.log(`📝 Prompt: "${loggableBody.prompt.substring(0, 100)}${loggableBody.prompt.length > 100 ? '...' : ''}"`);
       console.log(`🤖 Search Enabled: ${loggableBody.useGoogle ? 'YES' : 'NO'}`);
       console.log(`🤖 Model: ${loggableBody.model}`);
       console.log('📋 Request Body:', loggableBody);
       */

        // Optimistic updates
        // Get display name from available models for proper display
        const availableModels = selectAvailableModels(state);
        let modelNameForDisplay: string = modelToSend;
        
        try {
          const resolvedDisplayName = getDisplayNameFromModelName(modelToSend, availableModels);
          
          if (resolvedDisplayName) {
            modelNameForDisplay = resolvedDisplayName;
            console.log(`[chatCompletion] ✅ Model display name resolved for new message:`, {
              originalModelName: modelToSend,
              resolvedDisplayName,
              availableModelsCount: availableModels?.length || 0,
            });
          } else {
            console.warn(`[chatCompletion] ⚠️ Could not resolve display name for new message:`, {
              originalModelName: modelToSend,
              availableModelsCount: availableModels?.length || 0,
              availableModelNames: availableModels?.map(m => m.model_name) || [],
            });
            // modelNameForDisplay already set to modelToSend as fallback
          }
        } catch (error) {
          console.error(`[chatCompletion] 🚨 Error resolving model display name for new message:`, {
            originalModelName: modelToSend,
            error,
          });
          // modelNameForDisplay already set to modelToSend as fallback
        }

        dispatch(
          addUserMessage({
            role: 'assistant',
            content: '',
            thinking: true,
            isStreaming: true, // Add streaming flag
            model_display_name: modelNameForDisplay,
          }),
        );
        dispatch(setIsThinking(true));

        // Declare conversationIdReceived here to be accessible in both stream and non-stream blocks
        let conversationIdReceived = args.chat_session_id;
        console.log(
          `[apiSlice] chatCompletion mutation started. args.chat_session_id: ${args.chat_session_id}`,
        );

        try {
          // ALWAYS use the original endpoint
          const endpoint = `${apiBaseUrl || '/api/v0'}/general/chat/completions`;

          // Get token from session
          const session = await getSession();
          const token = session?.accessToken;
          const fetchHeaders: HeadersInit = {
            'Content-Type': 'application/json',
          };
          if (token) {
            fetchHeaders['Authorization'] = `Bearer ${token}`;
          }

          const response = await fetch(endpoint, {
            method: 'POST',
            headers: fetchHeaders,
            body: JSON.stringify(requestBody),
            signal: signal,
          });

          console.log(
            `[apiSlice] Fetch Response: ok=${response.ok}, status=${response.status}, contentType=${response.headers.get('content-type')}, bodyPresent=${!!response.body}`,
          );

          // --- Multi-LLM Logic: Smart reset selection ---
          const state = getState() as RootState;
          const conversationModel = selectConversationModel(state);

          // Only manage model selection state when user explicitly used @mention
          if (args.usedMention && selectedNextModel) {
            // For @mention usage, the selected model becomes the new default for this conversation
            dispatch(setConversationModel(selectedNextModel));
            // Keep it selected for UI consistency
            dispatch(setSelectedNextModelName(selectedNextModel));
          } else if (!args.usedMention) {
            // For non-@mention messages, clear the model selection so backend uses conversation's first model
            dispatch(setSelectedNextModelName(undefined));
            // Don't change conversationModel - let it remain as the first model used
          }
          // --- End Multi-LLM Logic ---

          if (!response.ok) {
            let errorData: any = {
              message: `Request failed with status ${response.status}`,
            };
            try {
              errorData = await response.json();
            } catch {
              /* Ignore */
            }

            if (errorData?.errorCode === 'FILE_PROCESSING_FAILED') {
              dispatch(
                setChatErrorMessage(
                  `The file may be corrupt, password-protected, or in an unsupported format. If the document has a security label, please set it to 'Public' and try again.`,
                ),
              );
            } else {
              const baseErrorMessage =
                errorData?.message ||
                errorData ||
                'Chat completion request failed';
              const errorMessage = `[${requestBody.model || 'Unknown Model'}] ${baseErrorMessage}`;
              dispatch(setChatErrorMessage(errorMessage));
            }

            dispatch(removeLastMessage());
            dispatch(setIsThinking(false));
            const fetchError: FetchBaseQueryError = {
              status: response.status || 'CUSTOM_ERROR',
              data: errorData.message || 'An error occurred',
              error: errorData.message || 'An error occurred',
            };
            return { error: fetchError };
          }

          // Handle Streaming Response: Explicitly check requestBody.stream, as content-type might be null
          if (requestBody.stream && response.body) {
            // Removed check for response.headers.get('content-type')
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let done = false;
            // conversationIdReceived is now declared at a higher scope

            while (!done && !signal.aborted) {
              let value: Uint8Array | undefined;
              try {
                const result = await reader.read();
                value = result.value;
                done = result.done;
              } catch (readError) {
                console.error('Error reading stream:', readError);
                throw readError;
              }

              if (value) {
                const chunk = decoder.decode(value, { stream: !done });
                const lines = chunk.split('\n').filter((line) => line.trim());

                for (const line of lines) {
                  if (line.startsWith('data: ')) {
                    const dataContent = line.substring(6).trim();
                    if (dataContent === '[DONE]') {
                      console.log(
                        `[apiSlice] 🏁 Stream [DONE] received for conversation ${conversationIdReceived} at ${new Date().toISOString()}`,
                      );
                      done = true;
                      break;
                    }
                    try {
                      const parsedChunk = JSON.parse(
                        dataContent,
                      ) as StreamChunk;

                      if (
                        'type' in parsedChunk &&
                        parsedChunk.type === 'sources'
                      ) {
                        console.log(
                          `📚 Search Results Received: ${parsedChunk.sources.length} sources`,
                        );
                        console.log(`📄 Sources:`, parsedChunk.sources);
                        dispatch(addSourcesToLastMessage(parsedChunk.sources));
                      } else if ('choices' in parsedChunk) {
                        if (
                          parsedChunk.conversation_uuid &&
                          !conversationIdReceived
                        ) {
                          conversationIdReceived =
                            parsedChunk.conversation_uuid;
                          console.log(
                            `[apiSlice] Streaming: Received conversation_uuid: ${conversationIdReceived}. Dispatching setConversationId.`,
                          );
                          dispatch(setConversationId(conversationIdReceived));
                        }
                        // Update model display name if received from backend
                        if (parsedChunk.model_display_name) {
                          dispatch(updateLastMessageModelDisplayName(parsedChunk.model_display_name));
                        }
                        const deltaContent =
                          parsedChunk.choices[0]?.delta?.content;
                        if (deltaContent) {
                          // Stop thinking when first content arrives
                          const currentState = getState() as RootState;
                          if (currentState.chat.isThinking) {
                            dispatch(setIsThinking(false));
                          }
                          dispatch(updateLastMessageContent(deltaContent));
                        }
                        if (parsedChunk.choices[0]?.finish_reason) {
                          done = true;
                          break;
                        }
                      } else {
                        console.warn(
                          'Received unexpected stream chunk structure:',
                          parsedChunk,
                        );
                      }
                    } catch (e) {
                      console.error(
                        'Failed to parse stream chunk:',
                        dataContent,
                        e,
                      );
                      // Don't process malformed chunks further to prevent them from leaking into content
                      if (dataContent.includes('"type":"sources"')) {
                        console.warn(
                          'Malformed sources chunk detected and ignored:',
                          dataContent.substring(0, 100) + '...',
                        );
                      }
                    }
                  }
                }
              }
            } // end while

            if (signal.aborted) {
              console.log('Stream aborted by client');
              try {
                if (response.body) response.body.cancel();
              } catch (e) {
                console.error('Error cancelling stream:', e);
              }
              dispatch(removeLastMessage());
              const abortError: FetchBaseQueryError = {
                status: 'CUSTOM_ERROR',
                data: 'Request aborted by client',
                error: 'Request aborted by client',
              };
              return { error: abortError };
            } else {
              dispatch(setIsThinking(false));
              dispatch(updateLastMessageStreamingStatus(false)); // Mark streaming as complete
              dispatch(setStreamJustCompleted(true)); // Mark that streaming just completed

              // Clear the flag after a delay
              setTimeout(() => {
                dispatch(setStreamJustCompleted(false));
              }, 6000); // Clear flag after 6 seconds

              // Only invalidate the history LIST (for sidebar), not the messages
              // This updates the conversation list without causing message overwrites

              setTimeout(() => {
                console.log(
                  `[apiSlice] 🕐 Invalidating history LIST for sidebar update`,
                );
                // Invalidate only the LIST tag to update sidebar without affecting messages
                dispatch(
                  apiSlice.util.invalidateTags([
                    { type: 'History', id: 'LIST' },
                  ]),
                );

                // Also invalidate the ChatMessages cache for this specific conversation
                // This ensures fresh data when returning to this chat
                if (conversationIdReceived) {
                  console.log(
                    `[apiSlice] 🔄 Invalidating ChatMessages cache for conversation ${conversationIdReceived}`,
                  );
                  dispatch(
                    apiSlice.util.invalidateTags([
                      { type: 'ChatMessages', id: conversationIdReceived },
                    ]),
                  );
                }
              }, 1500); // Reduced delay for better UX while ensuring conversation is saved

              console.log(
                `[apiSlice] ✅ Stream completed for conversation ${conversationIdReceived}`,
              );

              return { data: { success: true } };
            }
          } else if (!requestBody.stream && response.body) {
            // Handle Non-Streaming Response
            const nonStreamResponse = await response.json();
            // Assuming non-stream response contains the full message
            if (
              nonStreamResponse.choices &&
              nonStreamResponse.choices[0]?.message?.content
            ) {
              dispatch(setIsThinking(false)); // Stop thinking
              dispatch(updateLastMessageStreamingStatus(false)); // Not streaming
              dispatch(
                updateLastMessageContent(
                  nonStreamResponse.choices[0].message.content,
                ),
              );
              if (nonStreamResponse.sources) {
                // Handle sources if present in non-stream response
                dispatch(addSourcesToLastMessage(nonStreamResponse.sources));
              }
            }
            if (
              nonStreamResponse.conversation_uuid &&
              !conversationIdReceived
            ) {
              conversationIdReceived = nonStreamResponse.conversation_uuid;
              console.log(
                `[apiSlice] Non-Streaming: Received conversation_uuid: ${conversationIdReceived}. Dispatching setConversationId.`,
              );
              dispatch(setConversationId(nonStreamResponse.conversation_uuid));
            }
            dispatch(setIsThinking(false));
            // Only invalidate the history LIST for sidebar update
            setTimeout(() => {
              console.log(
                `[apiSlice] 🕐 Invalidating history LIST for sidebar update (non-streaming)`,
              );
              dispatch(
                apiSlice.util.invalidateTags([{ type: 'History', id: 'LIST' }]),
              );
            }, 1000); // Shorter delay for non-streaming
            console.log(
              `[apiSlice] ✅ Non-streaming response completed for conversation ${conversationIdReceived}`,
            );
            return { data: { success: true } }; // Indicate success
          } else {
            dispatch(removeLastMessage());
            throw new Error('Response body missing or content type incorrect.');
          }
        } catch (error: unknown) {
          console.error('chatCompletion queryFn failed:', error);
          const errorMessage =
            error instanceof Error ? error.message : String(error);
          dispatch(setChatErrorMessage(errorMessage || 'An error occurred'));
          dispatch(removeLastMessage());
          dispatch(setIsThinking(false));
          const fetchError: FetchBaseQueryError = {
            status: 'CUSTOM_ERROR',
            data: errorMessage || 'Unknown error',
            error: errorMessage || 'Unknown error',
          };
          return { error: fetchError };
        }
      },
    }),
  }),
  overrideExisting: false, // Important: keep existing endpoints
});

// Export hooks based on defined endpoints
export const {
  useGetModelsQuery,
  // useChatCompletionMutation, // Exported separately below
  useGetApiKeyMutation,
  useUpdateMessageLikeMutation,
  useSubmitHealthCheckPromptMutation,
  useGetRecentModelsQuery, // Export new hook
  useGetConversationHistoryQuery, // Export new hook
  useGetAllConversationHistoryQuery,
  useGetHistoryMessagesQuery, // Export new hook
  useUpdateConversationParamsMutation, // Export the new mutation hook
  useShareConversationMutation, // Export share conversation hook
  useAccessSharedConversationMutation, // Export access shared conversation hook
  useRenameConversationMutation,
  useDeleteConversationMutation,
  useGetTasksQuery,
  useGetPromptGalleryQuery,
  useCreatePromptGalleryMutation,
  useUpdatePromptGalleryMutation,
  useDeletePromptGalleryMutation,
  useSubmitFeedbackMutation,
  useRewritePromptMutation,
  useGeneratePromptMutation,
} = apiSlice;

// Export the injected endpoint hook separately
export const { useChatCompletionMutation } = extendedApi;
