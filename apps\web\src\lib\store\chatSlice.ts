import { createSlice, PayloadAction } from '@reduxjs/toolkit'; // Removed createAsyncThunk
import { RootState } from './store'; // Assuming RootState is defined here
import { GptModel } from '@/lib/types/common'; // Import shared GptModel type

// Removed local GptModel definition

// Define Source type (matching backend)
export interface Source {
  title: string;
  link: string;
  snippet: string;
}

// Type for individual messages
export interface Conversation {
  role: 'user' | 'assistant';
  content: string;
  messageId?: string; // Optional: ID from the backend or generated locally
  thinking?: boolean; // Thinking state for assistant messages
  isStreaming?: boolean; // Is the message currently streaming?
  thinkingStartTime?: number; // Optional: Track when thinking starts
  timestamp?: number; // For ordering/tracking
  attachments?: Array<{ name: string; type: string; size: number }>; // Renamed from 'files'
  like?: boolean | undefined; // Optional like status
  model_display_name?: string; // Optional: Track which model generated the message for display
  model_api_name?: string; // Optional: Original API model name for backend calls
  sources?: Source[]; // Optional: Add sources array
  usedMention?: boolean; // Optional: Track if user explicitly used @mention for this message
}

// Define the shape of the chat state
interface ChatState {
  chatErrorMessage?: string; // For displaying errors to the user
  // chatTemperature: number; // Current temperature setting - Moved to conversation params
  gptModel?: GptModel; // Currently selected GPT model
  shouldClearMessage: boolean; // Flag for triggering message clear
  messages: Conversation[]; // Array to store the conversation history
  isThinking: boolean; // Global flag for assistant thinking state
  conversationId?: string; // Conversation ID for backend tracking

  // Conversation-specific parameters
  instructions?: string | null;
  pastMessagesCount?: number | null;
  maxResponseTokens?: number | null;
  temperature?: number | null; // Renamed from chatTemperature
  topP?: number | null;

  // State for multi-LLM selection
  selectedNextModelName?: string | undefined; // Model name selected for the *next* message
  conversationModel?: string | undefined; // Model actively being used for this conversation

  // Track when streaming just completed to prevent race conditions
  streamJustCompleted?: boolean;

  // historyLoading removed - managed by RTK Query
  // historyError removed - managed by RTK Query
  // parentMessageId removed - managed by backend via conversationId
}

// BackendMessage interface removed - defined in apiSlice.ts

// REMOVED loadRawState helper function entirely

// Initial state - Use hardcoded defaults, remove all localStorage loading for this slice
const initialState: ChatState = {
  chatErrorMessage: undefined,
  // chatTemperature: 0.6, // Default temperature - Removed, use conversation param
  gptModel: undefined, // Initialize gptModel as undefined
  shouldClearMessage: false,
  messages: [], // Initialize messages as empty, history loaded from backend
  isThinking: false,
  conversationId: undefined, // Initialize conversationId as undefined

  // Initialize conversation parameters
  instructions: null,
  pastMessagesCount: 10, // Default value (matches user request)
  maxResponseTokens: 2000, // Default value
  temperature: 0.6, // Default value
  topP: 0.6, // Default value

  // Initialize multi-LLM state
  selectedNextModelName: undefined,
  conversationModel: undefined,

  // Initialize stream completion tracking
  streamJustCompleted: false,

  // historyLoading removed
  // historyError removed
  // parentMessageId removed
};

// --- Async Thunk fetchChatHistory removed ---

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    setChatErrorMessage: (state, action: PayloadAction<string | undefined>) => {
      state.chatErrorMessage = action.payload;
    },
    // setChatTemperature removed - managed by setConversationParameters
    setGptModel: (state, action: PayloadAction<GptModel | undefined>) => {
      state.gptModel = action.payload;
      // Remove saving selectedGptModel to localStorage to prevent JSON errors
      // if (typeof window !== 'undefined') {
      //   if (action.payload) {
      //     // localStorage.setItem('selectedGptModel', JSON.stringify(action.payload)); // REMOVED
      //   } else {
      //     localStorage.removeItem('selectedGptModel');
      //   }
      // }
    },
    triggerClearMessage: (state) => {
      state.shouldClearMessage = true;
    },
    messageCleared: (state) => {
      state.shouldClearMessage = false;
      state.messages = []; // Clear messages array in state
      state.conversationId = undefined; // Reset conversation ID in state
      // Reset conversation parameters to defaults
      state.instructions = initialState.instructions;
      state.pastMessagesCount = initialState.pastMessagesCount;
      state.maxResponseTokens = initialState.maxResponseTokens;
      state.temperature = initialState.temperature;
      state.topP = initialState.topP;
      // Reset model selections
      state.selectedNextModelName = undefined;
      state.conversationModel = undefined;
      // parentMessageId removed
      // Clear local storage for conversation context
      if (typeof window !== 'undefined') {
        // localStorage.removeItem('chatHistory'); // No longer storing history locally
        localStorage.removeItem('conversationId');
        // localStorage.removeItem('parentMessageId'); // parentMessageId removed
      }
    },
    // Renamed from setMessages, represents the currently *displayed* messages
    setChatMessages: (state, action: PayloadAction<Conversation[]>) => {
      state.messages = action.payload;
      // No longer save messages to localStorage
      // if (typeof window !== 'undefined') {
      //   localStorage.setItem('chatHistory', JSON.stringify(state.messages));
      // }
    },
    addUserMessage: (state, action: PayloadAction<Conversation>) => {
      // Adds a message to the *displayed* messages in the UI
      state.messages.push(action.payload);
      // No longer save messages to localStorage
      // if (typeof window !== 'undefined') {
      //   localStorage.setItem('chatHistory', JSON.stringify(state.messages));
      // }
    },
    // Updates the content of the last message (typically for streaming)
    updateLastMessageContent: (state, action: PayloadAction<string>) => {
      const lastMessageIndex = state.messages.length - 1;
      if (lastMessageIndex >= 0) {
        const lastMessage = state.messages[lastMessageIndex];
        if (lastMessage.role === 'assistant') {
          lastMessage.content += action.payload; // Append chunk
          // No longer save messages to localStorage
          // if (typeof window !== 'undefined') {
          //   localStorage.setItem('chatHistory', JSON.stringify(state.messages));
          // }
        }
      }
    },
    // Removes the last displayed message (e.g., if an error occurs during generation)
    removeLastMessage: (state) => {
      if (state.messages.length > 0) {
        state.messages.pop();
        // No longer save messages to localStorage
        //  if (typeof window !== 'undefined') {
        //     localStorage.setItem('chatHistory', JSON.stringify(state.messages));
        // }
      }
    },
    // Prepares for regeneration by removing the last assistant message
    prepareForRegeneration: (state) => {
      console.log('[REGEN SLICE] prepareForRegeneration called');
      console.log('[REGEN SLICE] Messages before:', state.messages.length, state.messages.map(m => ({ role: m.role, messageId: m.messageId, content: m.content?.substring(0, 50) + '...' })));
      
      // Find the last assistant message and remove it
      if (state.messages.length >= 2) {
        const lastMessage = state.messages[state.messages.length - 1];
        const secondLastMessage = state.messages[state.messages.length - 2];
        
        console.log('[REGEN SLICE] Last message role:', lastMessage.role);
        console.log('[REGEN SLICE] Second last message role:', secondLastMessage.role);
        
        // Ensure the last message is from assistant and second last is from user
        if (lastMessage.role === 'assistant' && secondLastMessage.role === 'user') {
          console.log('[REGEN SLICE] Removing last assistant message:', lastMessage.messageId);
          state.messages.pop(); // Remove the last assistant message
          console.log('[REGEN SLICE] Messages after removal:', state.messages.length, state.messages.map(m => ({ role: m.role, messageId: m.messageId, content: m.content?.substring(0, 50) + '...' })));
        } else {
          console.log('[REGEN SLICE] Invalid message sequence - not removing anything');
        }
      } else {
        console.log('[REGEN SLICE] Not enough messages for regeneration');
      }
    },
    setIsThinking: (state, action: PayloadAction<boolean>) => {
      state.isThinking = action.payload;
      // Also update the 'thinking' status on the last assistant message if it exists
      const lastMessageIndex = state.messages.length - 1;
      if (lastMessageIndex >= 0) {
        const lastMessage = state.messages[lastMessageIndex];
        if (lastMessage.role === 'assistant') {
          lastMessage.thinking = action.payload; // Update thinking status
          // No longer save messages to localStorage
          //  if (typeof window !== 'undefined') {
          //     localStorage.setItem('chatHistory', JSON.stringify(state.messages));
          //  }
        }
      }
    },
    setConversationId: (state, action: PayloadAction<string | undefined>) => {
      state.conversationId = action.payload;
      // Keep saving conversationId to localStorage if desired, but it won't be loaded on init anymore
      if (typeof window !== 'undefined') {
        if (action.payload) {
          localStorage.setItem('conversationId', action.payload);
        } else {
          localStorage.removeItem('conversationId');
        }
      }
    },
    // setParentMessageId removed
    // removeMessageById might still be useful for UI manipulation, but doesn't affect history persistence
    removeMessageById: (state, action: PayloadAction<string>) => {
      state.messages = state.messages.filter(
        (msg) => msg.messageId !== action.payload,
      );
      // No longer save messages to localStorage
      // if (typeof window !== 'undefined') {
      //   localStorage.setItem('chatHistory', JSON.stringify(state.messages));
      // }
    },
    updateMessageLikeStatus: (
      state,
      action: PayloadAction<{ messageId: string; liked: boolean | undefined }>,
    ) => {
      const message = state.messages.find(
        (msg) => msg.messageId === action.payload.messageId,
      );
      if (message) {
        // Toggle logic: If the clicked status is the same as current, reset to undefined. Otherwise, set to clicked status.
        message.like =
          message.like === action.payload.liked
            ? undefined
            : action.payload.liked;
        // No longer save messages to localStorage
        // if (typeof window !== 'undefined') {
        //     localStorage.setItem('chatHistory', JSON.stringify(state.messages));
        // }
      }
    },
    // Adds sources to the last message (typically the assistant's response)
    addSourcesToLastMessage: (state, action: PayloadAction<Source[]>) => {
      const lastMessageIndex = state.messages.length - 1;
      if (lastMessageIndex >= 0) {
        const lastMessage = state.messages[lastMessageIndex];
        if (lastMessage.role === 'assistant') {
          lastMessage.sources = action.payload;
          // No localStorage update needed
        }
      }
    },
    // Updates the model display name of the last message
    updateLastMessageModelDisplayName: (state, action: PayloadAction<string>) => {
      const lastMessageIndex = state.messages.length - 1;
      if (lastMessageIndex >= 0) {
        const lastMessage = state.messages[lastMessageIndex];
        if (lastMessage.role === 'assistant') {
          lastMessage.model_display_name = action.payload;
        }
      }
    },
    // Updates the streaming status of the last message
    updateLastMessageStreamingStatus: (state, action: PayloadAction<boolean>) => {
      const lastMessageIndex = state.messages.length - 1;
      if (lastMessageIndex >= 0) {
        const lastMessage = state.messages[lastMessageIndex];
        if (lastMessage.role === 'assistant') {
          lastMessage.isStreaming = action.payload;
        }
      }
    },
    // New reducer to set multiple conversation parameters at once
    setConversationParameters: (
      state,
      action: PayloadAction<{
        instructions?: string | null;
        pastMessagesCount?: number | null;
        maxResponseTokens?: number | null;
        temperature?: number | null;
        topP?: number | null;
      }>,
    ) => {
      const {
        instructions,
        pastMessagesCount,
        maxResponseTokens,
        temperature,
        topP,
      } = action.payload;
      if (instructions !== undefined) state.instructions = instructions;
      if (pastMessagesCount !== undefined)
        state.pastMessagesCount = pastMessagesCount;
      if (maxResponseTokens !== undefined)
        state.maxResponseTokens = maxResponseTokens;
      if (temperature !== undefined) state.temperature = temperature;
      if (topP !== undefined) state.topP = topP;
    },
    // New reducer for setting the model for the next message
    setSelectedNextModelName: (
      state,
      action: PayloadAction<string | undefined>,
    ) => {
      state.selectedNextModelName = action.payload;
      // When user explicitly selects a model, also set it as the conversation model
      if (action.payload) {
        state.conversationModel = action.payload;
      }
    },
    // New reducer for setting the conversation model (used for maintaining selection across messages)
    setConversationModel: (
      state,
      action: PayloadAction<string | undefined>,
    ) => {
      state.conversationModel = action.payload;
    },
    // New reducer for tracking stream completion
    setStreamJustCompleted: (state, action: PayloadAction<boolean>) => {
      state.streamJustCompleted = action.payload;
    },
  }, // This brace closes the reducers object
  // extraReducers removed
});

export const {
  setChatErrorMessage,
  // setChatTemperature removed
  setGptModel,
  triggerClearMessage,
  messageCleared,
  setChatMessages, // Export the renamed action
  addUserMessage,
  updateLastMessageContent,
  removeLastMessage,
  prepareForRegeneration, // Export the new regeneration action
  setConversationId,
  // setParentMessageId removed
  setIsThinking,
  removeMessageById,
  updateMessageLikeStatus,
  addSourcesToLastMessage, // Export the new action
  updateLastMessageModelDisplayName, // Export the new action
  updateLastMessageStreamingStatus, // Export the new action
  setConversationParameters, // Export the new action
  setSelectedNextModelName, // Export the new action for multi-LLM
  setConversationModel, // Export the new action for conversation model
  setStreamJustCompleted, // Export the new action for stream completion tracking
} = chatSlice.actions;

// Selectors
export const selectChatState = (state: RootState) => state.chat;
export const selectMessages = (state: RootState) => state.chat.messages;
export const selectIsThinking = (state: RootState) => state.chat.isThinking;
export const selectConversationId = (state: RootState) =>
  state.chat.conversationId;
// export const selectChatTemperature = (state: RootState) => state.chat.chatTemperature; // Removed selector
export const selectCurrentGptModel = (state: RootState) => state.chat.gptModel; // Added selector
// Add selectors for new parameters
export const selectInstructions = (state: RootState) => state.chat.instructions;
export const selectPastMessagesCount = (state: RootState) =>
  state.chat.pastMessagesCount;
export const selectMaxResponseTokens = (state: RootState) =>
  state.chat.maxResponseTokens;
export const selectTemperature = (state: RootState) => state.chat.temperature;
export const selectTopP = (state: RootState) => state.chat.topP;
// selectHistoryLoading removed
// selectHistoryError removed
// Add selector for the next model name
export const selectSelectedNextModelName = (
  state: RootState,
): string | undefined => state.chat.selectedNextModelName;
// Add selector for the conversation model
export const selectConversationModel = (
  state: RootState,
): string | null | undefined => state.chat.conversationModel;
// Add selector for stream completion tracking
export const selectStreamJustCompleted = (state: RootState): boolean =>
  state.chat.streamJustCompleted || false;
// Add other selectors as needed

export default chatSlice.reducer;
