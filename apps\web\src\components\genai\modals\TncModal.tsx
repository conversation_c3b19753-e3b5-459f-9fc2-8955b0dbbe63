'use client';

import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { signOut, useSession } from 'next-auth/react';
import { useAppSelector } from '@/lib/store/hooks'; // Use path alias
import { Theme, selectTheme } from '@/lib/store/uiSlice'; // Use path alias

// Define props interface
interface TncModalProps {
  show: boolean;
  onClose: () => void; // Passed from parent, likely dispatches closeTncModal
  agree: boolean; // Passed from parent (state.ui.tncModalShowAgree)
  agreeFn: () => void; // Passed from parent, likely dispatches action to accept T&C
}

const TncModal = ({ show, onClose, agree, agreeFn }: TncModalProps) => {
  // Removed closeFn as onClose should handle closing
  const { data: session } = useSession();
  const currentTheme = useAppSelector(selectTheme); // Get theme from Redux

  // Add null check for session
  const isStudent = session?.user?.type !== 'STAFF';
  const basePath = process.env.NEXT_PUBLIC_BASE_PATH;

  return (
    <Transition.Root show={show}>
      <Dialog
        as="div"
        className="relative z-30"
        onClose={onClose}
        __demoMode={true}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 dark:bg-[#4a4a4aA0] transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white dark:bg-[#3b3b3b] text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-3xl">
                <div className="bg-white dark:bg-[#3b3b3b] px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    {isStudent ? (
                      <div className="mt-3 text-left sm:ml-4 sm:mt-0 sm:text-left">
                        <Dialog.Title
                          as="h3"
                          className="text-lg font-semibold leading-6 text-gray-900"
                        >
                          Terms and Conditions for using “HKBU GenAI Platform”
                          (For Students)
                        </Dialog.Title>
                        <div className="my-4 text-sm text-gray-900">
                          <p className="pb-2">
                            The HKBU GenAI Platform (“GenAI Platform”) is based
                            on the generative artificial intelligence (“AI”)
                            language models developed by third-party AI service
                            providers including Alibaba Cloud, Microsoft Azure
                            OpenAI, and Google Cloud Platform. These terms and
                            conditions govern your usage of this GenAI Platform.
                            By using it, you unconditionally agree to the
                            following terms and conditions, as modified and/or
                            supplemented from time to time by the University:
                          </p>
                          <ol className="pl-2 list-decimal">
                            <li className="pb-2">
                              <b>Code of Conduct: </b>You are required to follow{' '}
                              <a
                                href="https://learn.microsoft.com/en-us/legal/cognitive-services/openai/code-of-conduct"
                                target="_blank"
                              >
                                Microsoft’s Code of Conduct for Azure OpenAI
                                Service
                              </a>{' '}
                              to ensure a respectful, safe, and inclusive
                              environment for all users and participants.
                            </li>
                            <li className="pb-2">
                              <b>Proper use of GenAI Platform: </b>This GenAI
                              Platform is provided for use by eligible HKBU
                              students and should be used only for study and
                              research activities related to their studies at
                              the University. Access to and use of the GenAI
                              Platform is subject to the guidelines and
                              regulations of the University.
                            </li>
                            <li className="pb-2">
                              <b>Research and learning with GenAI Platform: </b>
                              When using the GenAI Platform for research and
                              learning purposes, you should adhere to the{' '}
                              <a
                                href="https://securedoc.hkbu.edu.hk/hkbuOnly/ics/integration/GenAIPrinciples.pdf"
                                target="_blank"
                              >
                                Principles for the Use of Generative AI Tools in
                                Teaching and Learning, and Assessment
                              </a>{' '}
                              and{' '}
                              <a
                                href="https://ar.hkbu.edu.hk/quality-assurance/university-policy-and-guidelines/academic-integrity"
                                target="_blank"
                              >
                                Guidelines for Students on Academic Integrity
                              </a>
                              , among other relevant policies, which may be
                              updated from time to time.
                            </li>
                            <li className="pb-2">
                              <b>Quota: </b>For fairer and better use of the
                              University’s resources, a monthly limit will be
                              imposed. Once the limit is reached, a user is
                              suspended from access until the next calendar
                              month.
                            </li>
                            <li className="pb-2">
                              <b>Account security: </b>You are solely
                              responsible for all activities performed in this
                              GenAI Platform accessed through your SSOid. Do not
                              share your login information with anyone else. If
                              you suspect any unauthorised use of your account,
                              please immediately contact the ITO Service Centre
                              (Ext. 7899,{' '}
                              <a
                                href="mailto:<EMAIL>"
                                target="_blank"
                              >
                                <EMAIL>
                              </a>
                              ).
                            </li>
                            <li className="pb-2">
                              <b>Prohibited conduct: </b>You shall not abuse
                              this GenAI Platform or engage in any behaviour
                              that may disrupt the service. This includes but is
                              not limited to attempting to hack or misuse the
                              GenAI Platform, transmitting viruses or malware,
                              generating any inappropriate or unethical content
                              or using this GenAI Platform for any illegal
                              purpose.
                            </li>
                            <li className="pb-2">
                              <b>Limitation of liability: </b>By using this
                              GenAI Platform, you shall be liable for and shall
                              also indemnify and keep the University indemnified
                              from all loss or damage arising out of or in
                              connection with any improper use of the service.
                            </li>
                            <li className="pb-2">
                              <b>Modification, Suspension and Termination: </b>
                              This GenAI Platform may be modified or
                              discontinued at any time without prior notice. For
                              security reasons, the University reserves the
                              right to suspend or terminate your access to this
                              GenAI Platform without prior notice if any abuse,
                              suspicious activity or any kind of misbehaviour is
                              spotted under your account.
                            </li>
                            <li className="pb-2">
                              <b>Privacy: </b>The University is committed to
                              protecting your privacy and will only use your
                              personal data in accordance with the University’s{' '}
                              <i>
                                <a
                                  href="https://bupdpo.hkbu.edu.hk/"
                                  target="_blank"
                                >
                                  Privacy Policy Statement and Personal
                                  Information Collection Statement
                                </a>
                              </i>{' '}
                              and the relevant laws. For security reasons, all
                              information input into and relevant responses
                              produced by this GenAI Platform will be logged
                              according to the prevailing data policy of the
                              University. If such a log reveals evidence of any
                              suspected illegal activity, the University may
                              report such activity and submit the evidence
                              recorded in the log to the relevant law
                              enforcement agency.
                            </li>
                            <li className="pb-2">
                              <b>Governing Law and Jurisdiction: </b>These terms
                              and conditions shall be governed by and construed
                              in accordance with the laws of Hong Kong. The
                              courts of Hong Kong shall have exclusive
                              jurisdiction over any dispute arising from these
                              terms and conditions.
                            </li>
                          </ol>
                        </div>
                      </div>
                    ) : (
                      <div className="mt-3 text-left sm:ml-4 sm:mt-0 sm:text-left">
                        <Dialog.Title
                          as="h3"
                          className="text-lg font-semibold leading-6 text-gray-900 dark:text-white"
                        >
                          Terms and Conditions for using “HKBU GenAI Platform”
                          (For Staff)
                        </Dialog.Title>
                        <div className="my-4 text-sm text-gray-900 dark:text-white">
                          <p className="pb-2">
                            The HKBU GenAI Platform (“GenAI Platform”) is based
                            on the generative artificial intelligence (“AI”)
                            language model developed by third-party AI service
                            providers including Alibaba Cloud, Microsoft Azure
                            OpenAI, and Google Cloud Platform. These terms and
                            conditions govern your usage of this GenAI Platform.
                            By using it, you unconditionally agree to the
                            following terms and conditions, as modified and/or
                            supplemented from time to time by the University:
                          </p>
                          <ol className="pl-2 list-decimal">
                            <li className="pb-2">
                              <b>Introduction:</b> Generative AI chatbot built
                              on top of large language models (“LLMs”), a type
                              of deep learning algorithm that can recognise,
                              summarise, translate, predict and generate texts
                              and other content based on knowledge gained from
                              massive data sets, including information input by
                              users. The responses provided by this GenAI
                              Platform are based on LLM models provided by
                              third-party AI service providers including Alibaba
                              Cloud, Microsoft Azure OpenAI, and Google Cloud
                              Platform.
                            </li>
                            <li className="pb-2">
                              <b>
                                Compliance with the University’s{' '}
                                <i>Policy on Using AI-based Services</i>:
                              </b>{' '}
                              Access to and use of the GenAI Platform is subject
                              to the{' '}
                              <i>
                                <a
                                  href="https://securedoc.hkbu.edu.hk/staffOnly/ics/integration/Policy_on_using_AI-based_services.pdf"
                                  target="_blank"
                                >
                                  Policy on Using AI-based Services
                                </a>
                              </i>{' '}
                              and other relevant policies, guidelines and
                              regulations, including but not limited to the{' '}
                              <i>
                                <a
                                  href="https://securedoc.hkbu.edu.hk/staffOnly/ics/integration/Data_Governance_Policy.pdf"
                                  target="_blank"
                                >
                                  Data Governance Policy
                                </a>
                                ,{' '}
                                <a
                                  href="https://securedoc.hkbu.edu.hk/staffOnly/page.php?f=ics/integration/Privacy_Management_Programme.pdf"
                                  target="_blank"
                                >
                                  Privacy Management Programme
                                </a>
                              </i>{' '}
                              and{' '}
                              <i>
                                <a
                                  href="https://ito.hkbu.edu.hk/content/ito/en/_jcr_content.ssocheck.json?pathPdf=/content/dam/hongkongbaptistuniversity/ito-assets/doc/student-staff/security/guides/DLP-Policy.pdf"
                                  target="_blank"
                                >
                                  Data Leakage Prevention Policy
                                </a>
                              </i>
                              .
                            </li>
                            <li className="pb-2">
                              <b>Proper use of GenAI Platform:</b> This GenAI
                              Service is provided for use by eligible staff of
                              the University and should be used only for work
                              and research-related purposes. Using this GenAI
                              Service for personal reasons or non-work-related
                              activities is prohibited. For fairer and better
                              use of the University’s resources, a daily limit
                              may be imposed.
                            </li>
                            <li className="pb-2">
                              <b>Account security:</b> You are solely
                              responsible for all activity performed in this
                              GenAI Platform accessed through your SSOid. Do not
                              share your login information with anyone else. If
                              you suspect any unauthorised use of your account,
                              please immediately contact the ITO Service Centre
                              (Ext. 7899,{' '}
                              <a
                                target="_blank"
                                href="mailto:<EMAIL>"
                              >
                                <EMAIL>
                              </a>
                              ).
                            </li>
                            <li className="pb-2">
                              <b>Prohibited conduct:</b> You shall not abuse
                              this GenAI Platform or engage in any behaviour
                              that may disrupt the service. This includes but is
                              not limited to attempting to hack or misuse the
                              GenAI Platform, transmitting viruses or malware,
                              generating any inappropriate or unethical content
                              or using this GenAI Platform for any illegal
                              purpose.
                            </li>
                            <li className="pb-2">
                              <b>Limitation of liability:</b> By using this
                              GenAI Platform, you shall be liable for and shall
                              also indemnify and keep the University indemnified
                              from all loss or damage arising out of or in
                              connection with any improper use of the
                              service.{' '}
                            </li>
                            <li className="pb-2">
                              <b>Termination:</b> This GenAI Platform is made
                              available on a trial basis and may be modified or
                              discontinued at any time without prior notice. For
                              security reasons, the University reserves the
                              right to terminate your access to this GenAI
                              Service without prior notice if any abuse or
                              suspicious activity is spotted under your
                              account.{' '}
                            </li>
                            <li className="pb-2">
                              <b>Privacy:</b> The University is committed to
                              protecting your privacy and will only use your
                              personal data in accordance with the University’s{' '}
                              <a
                                target="_blank"
                                href="https://bupdpo.hkbu.edu.hk/"
                              >
                                <i>
                                  Privacy Policy Statement and Personal
                                  Information Collection Statement
                                </i>
                              </a>{' '}
                              and{' '}
                              <a
                                target="_blank"
                                href="https://securedoc.hkbu.edu.hk/staffOnly/page.php?f=ics/integration/Privacy_Management_Programme.pdf"
                              >
                                <i>Privacy Management Programme</i>
                              </a>
                              .{' '}
                              <b
                                style={{
                                  color:
                                    currentTheme === Theme.dark // Use theme from Redux state
                                      ? '#f20d0d' // Consider using Tailwind classes instead of inline styles
                                      : 'red',
                                  fontWeight: 'bold',
                                  fontStyle: 'italic',
                                }}
                              >
                                For security reasons, all information input into
                                and relevant responses produced by this GenAI
                                Service will be logged.{' '}
                              </b>{' '}
                              If such a log reveals evidence of any suspected
                              illegal activity, the University may report such
                              activity and submit the evidence recorded in the
                              log to the relevant law enforcement agency.
                            </li>
                            <li className="pb-2">
                              <b>
                                Using GenAI for research, teaching and learning:
                              </b>{' '}
                              If you use the GenAI Platform to support any
                              research, teaching or learning activities, you
                              should follow the applicable policies of the
                              University as updated from time to time.
                            </li>
                            <li className="pb-2">
                              <b>Governing Law and Jurisdiction:</b> These terms
                              and conditions shall be governed by and construed
                              in accordance with the laws of Hong Kong. The
                              courts of Hong Kong shall have exclusive
                              jurisdiction over any dispute arising from these
                              terms and conditions.
                            </li>
                          </ol>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-[#4a4a4a] px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                  {agree ? (
                    <>
                      <button
                        type="button"
                        className="inline-flex w-full justify-center rounded-2xl bg-emerald-700 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-emerald-600 sm:ml-3 sm:w-auto"
                        onClick={() => {
                          agreeFn();
                        }}
                      >
                        Agree
                      </button>
                      <button
                        type="button"
                        className="mt-3 inline-flex w-full justify-center rounded-2xl bg-white dark:bg-[#3b3b3b] px-3 py-2 text-sm font-semibold text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 dark:hover:bg-[#4a4a4a] sm:mt-0 sm:w-auto"
                        onClick={() => {
                          signOut({
                            callbackUrl: `${basePath}`,
                          });
                        }}
                      >
                        Disagree
                      </button>
                    </>
                  ) : (
                    <>
                      <button
                        type="button"
                        className="mt-3 inline-flex w-full justify-center rounded-2xl bg-white dark:bg-[#3b3b3b] px-3 py-2 text-sm font-semibold text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 dark:hover:bg-[#4a4a4a] sm:mt-0 sm:w-auto"
                        onClick={onClose} // Use onClose prop
                      >
                        Close
                      </button>
                    </>
                  )}
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default TncModal;
