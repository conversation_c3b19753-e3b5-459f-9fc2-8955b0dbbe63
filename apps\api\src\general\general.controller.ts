import {
  Controller,
  Post,
  Get,
  Put,
  Patch, // Add Patch here
  Body,
  Res,
  Req,
  UseGuards,
  Ip,
  ValidationPipe,
  Param,
  Query,
  DefaultValuePipe,
  ParseIntPipe, // Added Query, DefaultValuePipe, ParseIntPipe
  HttpCode,
  HttpStatus,
  UploadedFile,
  ParseFilePipe,
  UseInterceptors,
  ForbiddenException,
  Logger, // Import Logger
  BadRequestException,
  NotFoundException, // Import NotFoundException
  InternalServerErrorException, // Keep this for general errors
  ParseUUIDPipe, // Assuming deploymentName might be a UUID or similar string
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ConfigService } from '@nestjs/config';
import { Response, Request, Express } from 'express';
import { GeneralService } from './general.service';
import { OcrService } from '../utils/ocr.service'; // Added import
import { ChatCompletionService } from './chat/chat-completion/chat-completion.service'; // Import ChatCompletionService
import { UpdateConversationParamsDto } from './chat/chat-completion/dto/UpdateConversationParamsDto'; // Import DTO
import { CreateFeedbackDto } from './dto/create-feedback.dto';
import { ReactionDto } from './dto/reaction.dto';
// InitRequestDto is no longer needed if init endpoint is removed
// import { InitRequestDto } from './dto/init-request.dto';
// StreamChatDto might be handled by a different controller now
// import { StreamChatDto } from './dto/stream-chat.dto';
// Import LLM services via GeneralService, no direct injection needed here
import { AuthGuard } from '@nestjs/passport';
import { Readable } from 'stream';
import { AuthenticatedUser } from '../auth/user.interface'; // Revert to relative path
import { v4 as uuidv4 } from 'uuid'; // Import uuid
// Ensure getModelType is correctly exported from prompt-processing or define it here
import { getModelType } from '../utils/prompt-processing'; // Assuming it's exported correctly
import { PaginatedConversationHistoryResponseDto } from './dto/paginated-history.dto'; // Import the new DTO
import { GeneralRateLimitGuard } from '../common/guards/general-rate-limit.guard';

// DTO for recording usage (moved from recent-models.controller)
import { IsNumber } from 'class-validator';
class RecordUsageDto {
  @IsNumber()
  modelId!: number;
}

@Controller('general') // Base path remains /api/general
@UseGuards(AuthGuard('jwt'), GeneralRateLimitGuard)
export class GeneralController {
  // Inject Logger
  private readonly logger = new Logger(GeneralController.name);

  constructor(
    private readonly configService: ConfigService, // Keep if needed elsewhere
    private readonly generalService: GeneralService,
    private readonly ocrService: OcrService, // Inject OcrService
    private readonly chatCompletionService: ChatCompletionService, // Inject ChatCompletionService
    // LLM services are injected into GeneralService, not controller
  ) {}

  @Post('feedback')
  @HttpCode(HttpStatus.CREATED)
  async submitFeedback(
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    feedbackData: CreateFeedbackDto,
    @Req() req: Request,
    @Ip() ip: string,
  ) {
    // Temporarily hardcode user for testing
    // const user = { userId: 'ken-chow', email: '<EMAIL>', type: 'STAFF', dept_unit_code: 'TEST' } as AuthenticatedUser;
    const user = req.user as AuthenticatedUser;
    if (!user?.userId) {
      throw new ForbiddenException(
        'User information not available. Please ensure you are logged in.',
      );
    }
    // Call the correct service method
    return this.generalService.handleFeedback(feedbackData, user, ip);
  }

  @Get('agreed-tnc')
  async getAgreedTnc(@Req() req: Request) {
    // Temporarily hardcode user for testing
    // const user = { userId: 'ken-chow' } as AuthenticatedUser;
    const user = req.user as AuthenticatedUser;
    if (!user?.userId) {
      throw new ForbiddenException('User ID not found on request.');
    }
    // Call the correct service method
    return this.generalService.checkTncStatus(user.userId);
  }

  @Post('agreed-tnc')
  @HttpCode(HttpStatus.OK)
  async setAgreedTnc(@Req() req: Request) {
    // Temporarily hardcode user for testing
    // const user = { userId: 'ken-chow' } as AuthenticatedUser;
    const user = req.user as AuthenticatedUser;
    if (!user?.userId) {
      throw new ForbiddenException('User ID not found on request.');
    }
    // Call the correct service method
    return this.generalService.setTncStatus(user.userId);
  }

  @Post('api-key')
  @HttpCode(HttpStatus.CREATED)
  async createApiKey(@Req() req: Request, @Ip() ip: string) {
    // Temporarily hardcode user for testing
    // const user = { userId: 'ken-chow' } as AuthenticatedUser;
    const user = req.user as AuthenticatedUser;
    if (!user?.userId) {
      throw new ForbiddenException('User ID not found on request.');
    }
    // Call the correct service method
    return this.generalService.generateApiKey(user.userId, ip);
  }

  @Post('get_notify')
  @HttpCode(HttpStatus.OK)
  async getNotify(@Req() req: Request) {
    // Temporarily hardcode user for testing
    // const user = { userId: 'ken-chow' } as AuthenticatedUser;
    const user = req.user as AuthenticatedUser;
    if (!user?.userId) {
      throw new ForbiddenException('User ID not found on request.');
    }
    // Call the correct service method
    return this.generalService.getNotifyStatus(user.userId);
  }

  @Put('notify') // Use PUT for updating status
  @HttpCode(HttpStatus.OK)
  async setNotify(@Req() req: Request): Promise<{ success: boolean }> {
    // Temporarily hardcode user for testing
    // const user = { userId: 'ken-chow' } as AuthenticatedUser;
    const user = req.user as AuthenticatedUser;
    if (!user?.userId) {
      throw new ForbiddenException('User ID not found on request.');
    }
    this.logger.log(`Setting notify status for user ${user.userId}`);
    return this.generalService.setNotifyStatus(user.userId);
  }

  @Get('model_list')
  @HttpCode(HttpStatus.OK)
  async getModelList(@Req() req: Request) {
    // Temporarily hardcode user for testing
    // const userId = 'ken-chow';
    // const userType = 'STAFF';
    const user = req.user as AuthenticatedUser;
    if (!user?.userId || !user?.type) {
      throw new ForbiddenException('User details not found on request.');
    }
    // Call the correct service method with hardcoded values
    return this.generalService.getModelList(user.userId, user.type);
  }

  // Removed deprecated init endpoint

  @Post('ocr')
  @UseInterceptors(FileInterceptor('file'))
  @HttpCode(HttpStatus.OK)
  async performOcr(
    @UploadedFile(
      new ParseFilePipe({
        /* Add appropriate validators here */
      }),
    )
    file: Express.Multer.File,
    @Req() req: Request,
  ) {
    // Temporarily hardcode user for testing
    // const user = { userId: 'ken-chow', type: 'STAFF', dept_unit_code: 'TEST' } as AuthenticatedUser;
    const user = req.user as AuthenticatedUser;
    if (!user?.userId) {
      // Check user exists
      throw new ForbiddenException('User details not found on request.');
    }
    if (!file) {
      throw new BadRequestException('File is required for OCR.');
    }
    // Call the new OcrService method directly
    const extractedText = await this.ocrService.extractTextFromFile(
      file.buffer,
      user.dept_unit_code,
      file.originalname,
    );
    return { success: true, result: extractedText };
  }

  // --- Methods moved from RecentModelsController ---

  @Get('recent-models') // Path relative to /general
  @HttpCode(HttpStatus.OK)
  async getRecentModels(
    @Req() req: Request,
    @Query('limit', new DefaultValuePipe(4), ParseIntPipe) limit: number, // Optional limit query param
  ) {
    // Temporarily hardcode user for testing
    // const user = { userId: 'ken-chow' } as AuthenticatedUser;
    const user = req.user as AuthenticatedUser;

    this.logger.log(`[ENDPOINT] GET /general/recent-models - Request received`);
    this.logger.debug(`[ENDPOINT] User from JWT: ${JSON.stringify(user)}`);

    if (!user?.userId) {
      this.logger.error(
        `[ENDPOINT] GET /general/recent-models - No userId found in JWT`,
      );
      throw new ForbiddenException('User information not available.');
    }

    this.logger.log(
      `[ENDPOINT] GET /general/recent-models - Called by user "${user.userId}" with limit ${limit}`,
    );

    try {
      const result = await this.generalService.getRecentModels(
        user.userId,
        limit,
      );
      this.logger.log(
        `[ENDPOINT] GET /general/recent-models - Returning ${result.length} models for user "${user.userId}"`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `[ENDPOINT] GET /general/recent-models - Error for user "${user.userId}": ${error}`,
      );
      throw error;
    }
  }

  @Post('recent-models/usage') // Path relative to /general
  @HttpCode(HttpStatus.OK) // Or HttpStatus.CREATED if preferred
  async recordModelUsage(
    @Req() req: Request,
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    body: RecordUsageDto,
  ) {
    // Temporarily hardcode user for testing
    // const user = { userId: 'ken-chow' } as AuthenticatedUser;
    const user = req.user as AuthenticatedUser;
    if (!user?.userId) {
      throw new ForbiddenException('User information not available.');
    }
    this.logger.log(
      `POST /general/recent-models/usage called by user ${user.userId} for model ${body.modelId}`,
    );
    // Assuming GeneralService now has recordModelUsage method
    return this.generalService.recordModelUsage(user.userId, body.modelId);
  }

  // --- Method moved from ChatController ---

  @Get('conversations/history') // Path relative to /general
  @HttpCode(HttpStatus.OK)
  async getConversationHistory(
    @Req() req: Request,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ): Promise<PaginatedConversationHistoryResponseDto> {
    // Temporarily hardcode user for testing
    // const user = { userId: 'ken-chow' } as AuthenticatedUser;
    const user = req.user as AuthenticatedUser;
    if (!user?.userId) {
      throw new ForbiddenException('User information not available.');
    }
    this.logger.log(
      `GET /general/conversations/history called by user ${user.userId} with page: ${page}, limit: ${limit}`,
    );
    return this.generalService.getConversationHistory(user.userId, page, limit);
  }

  // --- Endpoint to update conversation parameters ---
  @Patch('chat/parameters') // New endpoint
  @HttpCode(HttpStatus.NO_CONTENT) // Use 204 No Content for successful update
  @UseGuards(AuthGuard('jwt')) // Re-enable when auth is active
  async updateChatParameters(
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    updateParamsDto: UpdateConversationParamsDto,
    @Req() req: Request,
  ) {
    // Temporarily hardcode user for testing
    // const user = { userId: 'ken-chow' } as AuthenticatedUser;
    const user = req.user as AuthenticatedUser;
    if (!user?.userId) {
      throw new ForbiddenException('User information not available.');
    }

    this.logger.log(
      `PATCH /general/chat/parameters called by user ${user.userId} for session ${updateParamsDto.chat_session_id}`,
    );
    // Call the method directly on the injected ChatCompletionService
    await this.chatCompletionService.updateConversationParams(
      updateParamsDto,
      user.userId,
    );
    // No content returned on success
  }

  @Post('reaction')
  @HttpCode(HttpStatus.OK)
  async updateMessageReaction(
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    reactionDto: ReactionDto,
    @Req() req: Request,
  ) {
    const user = req.user as AuthenticatedUser;
    if (!user?.userId) {
      throw new ForbiddenException('User information not available.');
    }

    this.logger.log(
      `POST /general/reaction called by user ${user.userId} for message ${reactionDto.messageId} with reaction ${reactionDto.reaction}`,
    );

    await this.generalService.updateMessageReaction(
      reactionDto.messageId,
      reactionDto.reaction,
      user.userId,
    );

    return { success: true };
  }
}
// Removed extra closing brace
