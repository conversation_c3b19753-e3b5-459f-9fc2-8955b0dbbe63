/**
 * Utility functions for determining model capabilities
 * Matches backend logic for consistent behavior
 */

export interface ModelCapabilities {
  supportsTools: boolean;
  supportsSystemMessages: boolean;
  supportsSearch: boolean;
}

/**
 * Determines if a model supports tool/function calling
 * This should match the backend logic in ChatCompletionService
 */
export function getModelCapabilities(
  modelName?: string | null,
): ModelCapabilities {
  if (!modelName) {
    return {
      supportsTools: false,
      supportsSystemMessages: true,
      supportsSearch: true, // All models support search via keyword extraction
    };
  }

  const modelNameLower = modelName.toLowerCase();

  // Models that explicitly don't support tools (matches backend TOOL_UNSUPPORTED_MODELS)
  const toolUnsupportedModels = [
    'o1-mini', // o1-mini specifically doesn't support function calling
    'deepseek-', // DeepSeek models hosted on Azure AI Service don't support OpenAI-compatible endpoints
  ];

  // Check if model is explicitly unsupported for tools
  const supportsTools = !toolUnsupportedModels.some((unsupported) =>
    modelNameLower.includes(unsupported),
  );

  // Models that don't support system messages (matches backend pattern)
  const systemMessageUnsupportedPatterns = [
    /^o1($|-)/, // o1, o1-mini, o1-preview, etc.
    /^o3($|-)/, // o3, o3-mini, etc.
  ];

  const supportsSystemMessages = !systemMessageUnsupportedPatterns.some(
    (pattern) => pattern.test(modelNameLower),
  );

  // All models support search via keyword extraction approach
  const supportsSearch = true;

  return {
    supportsTools,
    supportsSystemMessages,
    supportsSearch,
  };
}

/**
 * Checks if a model supports tool/function calling
 */
export function modelSupportsTools(modelName?: string | null): boolean {
  return getModelCapabilities(modelName).supportsTools;
}

/**
 * Checks if a model supports system messages
 */
export function modelSupportsSystemMessages(
  modelName?: string | null,
): boolean {
  return getModelCapabilities(modelName).supportsSystemMessages;
}

/**
 * Checks if a model supports search functionality
 */
export function modelSupportsSearch(modelName?: string | null): boolean {
  return getModelCapabilities(modelName).supportsSearch;
}

/**
 * Gets a user-friendly explanation for why a feature is unavailable
 */
export function getFeatureUnavailableReason(
  feature: 'search' | 'systemMessages',
  modelName?: string | null,
): string {
  if (!modelName) {
    return `${feature === 'search' ? 'Search functionality' : 'System messages'} require a model to be selected`;
  }

  const capabilities = getModelCapabilities(modelName);

  if (feature === 'search' && !capabilities.supportsSearch) {
    return `Search functionality is not available for ${modelName}`;
  }

  if (feature === 'systemMessages' && !capabilities.supportsSystemMessages) {
    return `System messages are not supported by ${modelName}`;
  }

  return ''; // Feature is available
}
