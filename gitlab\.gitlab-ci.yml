stages:
  - prepare-env
  - generate-prisma
  - build
  - stop-n01
  - deploy-n01
  - start-n01
  - stop-n02
  - deploy-n02
  - start-n02

# Prepare Environment
prepare-env-webs-n01:
  stage: prepare-env
  tags:
    - uat
    - n01
    - genaiplatform
  script:
    - chmod +x ./gitlab/script/prepare-web-env.sh
    - dos2unix ./gitlab/script/prepare-web-env.sh
    - bash ./gitlab/script/prepare-web-env.sh
    - cat ./apps/web/.env
  environment:
    name: uat_web
  artifacts:
    paths:
      - ./apps/web/.env
  only:
    - testing

prepare-env-api-n01:
  stage: prepare-env
  tags:
    - uat
    - n01
    - genaiplatform
  script:
    - chmod +x ./gitlab/script/prepare-api-env.sh
    - dos2unix ./gitlab/script/prepare-api-env.sh
    - bash ./gitlab/script/prepare-api-env.sh
    - cat ./apps/api/.env
  environment:
    name: uat_api
  artifacts:
    paths:
      - ./apps/api/.env
  only:
    - testing


prepare-env-prisma-n01:
  stage: prepare-env
  tags:
    - uat
    - n01
    - genaiplatform
  script:
    - chmod +x ./gitlab/script/prepare-prisma-env.sh
    - dos2unix ./gitlab/script/prepare-prisma-env.sh
    - bash ./gitlab/script/prepare-prisma-env.sh
  environment:
    name: uat_prisma
  artifacts:
    paths:
      - ./packages/database/.env
  only:
    - testing

prepare-env-webs-n02:
  stage: prepare-env
  tags:
    - uat
    - n02
    - genaiplatform
  script:
    - chmod +x ./gitlab/script/prepare-web-env.sh
    - dos2unix ./gitlab/script/prepare-web-env.sh
    - bash ./gitlab/script/prepare-web-env.sh
    - cat ./apps/web/.env
  environment:
    name: uat_web
  artifacts:
    paths:
      - ./apps/web/.env
  only:
    - testing

prepare-env-api-n02:
  stage: prepare-env
  tags:
    - uat
    - n02
    - genaiplatform
  script:
    - chmod +x ./gitlab/script/prepare-api-env.sh
    - dos2unix ./gitlab/script/prepare-api-env.sh
    - bash ./gitlab/script/prepare-api-env.sh
    - cat ./apps/api/.env
  environment:
    name: uat_api
  artifacts:
    paths:
      - ./apps/api/.env
  only:
    - testing

prepare-env-prisma-n02:
  stage: prepare-env
  tags:
    - uat
    - n02
    - genaiplatform
  script:
    - chmod +x ./gitlab/script/prepare-prisma-env.sh
    - dos2unix ./gitlab/script/prepare-prisma-env.sh
    - bash ./gitlab/script/prepare-prisma-env.sh
  environment:
    name: uat_prisma
  artifacts:
    paths:
      - ./packages/database/.env
  only:
    - testing

# Build Applications
generate-prisma-client:
  stage: generate-prisma
  needs: ["prepare-env-prisma-n01"]
  tags:
    - uat
    - n01
    - genaiplatform
  script:
    - pnpm install --no-frozen-lockfile
    - pnpm --filter=@hkbu-genai-platform/database build
  artifacts:
    paths:
      - ./packages/database/prisma/generated
    expire_in: 1 day
  only:
    - testing

build-api:
  stage: build
  needs: ["generate-prisma-client", "prepare-env-api-n01"]
  tags:
    - uat
    - n01
    - genaiplatform
  script:
    - pnpm install --no-frozen-lockfile
    - pnpm turbo run build --filter=api
  artifacts:
    paths:
      - ./apps/api/dist
      - ./apps/api/.env
    expire_in: 1 day
  only:
    - testing

build-web:
  stage: build
  needs: ["generate-prisma-client", "prepare-env-webs-n01"]
  tags:
    - uat
    - n01
    - genaiplatform
  script:
    - pnpm install --no-frozen-lockfile
    - pnpm turbo run build --filter=web
  artifacts:
    paths:
      - ./apps/web/.next
      - ./apps/web/.env
      - ./apps/web/public
    expire_in: 1 day
  only:
    - testing

# Rolling Deploy N01
stop-api-n01:
  stage: stop-n01
  tags:
    - uat
    - n01
    - genaiplatform
  script:
    - pm2 delete genai-api-app || true
  only:
    - testing

stop-web-n01:
  stage: stop-n01
  tags:
    - uat
    - n01
    - genaiplatform
  script:
    - pm2 delete genai-web-app || true
  only:
    - testing

deploy-api-n01:
  stage: deploy-n01
  needs: ["build-api", "stop-api-n01", "generate-prisma-client"]
  tags:
    - uat
    - n01
    - genaiplatform
  script:
    - rm -rf ~/genai-platform/api
    - pnpm deploy --filter=@hkbu-genai-platform/api ~/genai-platform/api --legacy --ignore-scripts
    - cp ./apps/api/.env ~/genai-platform/api/
    - cp ./gitlab/script/ecosystem.config.json ~/genai-platform/api/
  only:
    - testing

deploy-web-n01:
  stage: deploy-n01
  needs: ["build-web", "stop-web-n01", "generate-prisma-client"]
  tags:
    - uat
    - n01
    - genaiplatform
  script:
    - rm -rf ~/genai-platform/web
    - pnpm deploy --filter=web ~/genai-platform/web --legacy --ignore-scripts
    - cp -r ./apps/web/.next ~/genai-platform/web/
    - cp -r ./apps/web/public ~/genai-platform/web/
    - cp ./apps/web/.env ~/genai-platform/web/
    - cp ./gitlab/script/ecosystem.config.json ~/genai-platform/web/
    - mkdir -p ~/genai-platform/web/prisma/generated
    - cp -r ./packages/database/prisma/generated/. ~/genai-platform/web/prisma/generated/
  only:
    - testing

start-api-n01:
  stage: start-n01
  needs: ["deploy-api-n01"]
  tags:
    - uat
    - n01
    - genaiplatform
  script:
    - cd ~/genai-platform/api
    - pm2 start ecosystem.config.json --only genai-api-app
  only:
    - testing

start-web-n01:
  stage: start-n01
  needs: ["deploy-web-n01"]
  tags:
    - uat
    - n01
    - genaiplatform
  script:
    - cd ~/genai-platform/web
    - pm2 start ecosystem.config.json --only genai-web-app
  only:
    - testing

# Rolling Deploy N02
stop-api-n02:
  stage: stop-n02
  needs: ["start-api-n01", "start-web-n01"]
  tags:
    - uat
    - n02
    - genaiplatform
  script:
    - pm2 delete genai-api-app || true
  only:
    - testing

stop-web-n02:
  stage: stop-n02
  needs: ["start-api-n01", "start-web-n01"]
  tags:
    - uat
    - n02
    - genaiplatform
  script:
    - pm2 delete genai-web-app || true
  only:
    - testing

deploy-api-n02:
  stage: deploy-n02
  needs: ["build-api", "stop-api-n02", "generate-prisma-client"]
  tags:
    - uat
    - n02
    - genaiplatform
  script:
    - rm -rf ~/genai-platform/api
    - pnpm deploy --filter=@hkbu-genai-platform/api ~/genai-platform/api --legacy --ignore-scripts
    - cp ./apps/api/.env ~/genai-platform/api/
    - cp ./gitlab/script/ecosystem.config.json ~/genai-platform/api/
  only:
    - testing

deploy-web-n02:
  stage: deploy-n02
  needs: ["build-web", "stop-web-n02", "generate-prisma-client"]
  tags:
    - uat
    - n02
    - genaiplatform
  script:
    - rm -rf ~/genai-platform/web
    - pnpm deploy --filter=web ~/genai-platform/web --legacy --ignore-scripts
    - cp -r ./apps/web/.next ~/genai-platform/web/
    - cp -r ./apps/web/public ~/genai-platform/web/
    - cp ./apps/web/.env ~/genai-platform/web/
    - cp ./gitlab/script/ecosystem.config.json ~/genai-platform/web/
    - mkdir -p ~/genai-platform/web/prisma/generated
    - cp -r ./packages/database/prisma/generated/. ~/genai-platform/web/prisma/generated/
  only:
    - testing

start-api-n02:
  stage: start-n02
  needs: ["deploy-api-n02"]
  tags:
    - uat
    - n02
    - genaiplatform
  script:
    - cd ~/genai-platform/api
    - pm2 start ecosystem.config.json --only genai-api-app
  only:
    - testing

start-web-n02:
  stage: start-n02
  needs: ["deploy-web-n02"]
  tags:
    - uat
    - n02
    - genaiplatform
  script:
    - cd ~/genai-platform/web
    - pm2 start ecosystem.config.json --only genai-web-app
  only:
    - testing
