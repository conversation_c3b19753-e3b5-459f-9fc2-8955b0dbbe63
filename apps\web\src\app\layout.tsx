'use client';

import React, { useEffect, Suspense } from 'react';
import GoogleAnalytics from '@/components/genai/GoogleAnalytics';
import ThemeRegistry from '@/components/ThemeRegistry/ThemeRegistry';
import StoreProvider from '@/lib/store/StoreProvider';
import './globals.css';
import AuthProvider from '@/app/providers/AuthProvider';
import { useSession } from 'next-auth/react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import MenuIcon from '@mui/icons-material/Menu';
import ChatHistorySidebar, {
  SIDEBAR_WIDTH_COLLAPSED,
  SIDEBAR_WIDTH_EXPANDED,
} from '@/components/genai/ChatHistorySidebar';
import {
  ChatLayoutProvider,
  useChatLayout,
} from '@/contexts/ChatLayoutContext';
import { useTheme } from '@mui/material';
import { ConfigProvider } from '@/contexts/ConfigContext';

// Separate component for handling search params that needs Suspense
function FullscreenHandler({ children }: { children: React.ReactNode }) {
  const searchParams = useSearchParams();
  const isFullscreen = searchParams.get('fullscreen') === 'true';

  if (isFullscreen) {
    return (
      <Box
        sx={{
          width: '100vw',
          minHeight: '100svh',
          overflow: 'auto',
        }}
      >
        {children}
      </Box>
    );
  }

  return <>{children}</>;
}

// Component that handles authenticated layout with fullscreen support
function AuthenticatedLayout({ children, isNotFound }: { children: React.ReactNode; isNotFound: boolean }) {
  const { isSidebarExpanded, toggleSidebar, setIsSidebarExpanded } = useChatLayout();
  const theme = useTheme();
  const searchParams = useSearchParams();
  const isFullscreen = searchParams.get('fullscreen') === 'true';

  const handleContentClick = () => {
    if (isSidebarExpanded && window.innerWidth < theme.breakpoints.values.md) {
      setIsSidebarExpanded(false);
    }
  };

  // In fullscreen mode, render children directly without sidebar
  if (isFullscreen) {
    return <>{children}</>;
  }

  // Normal authenticated layout with sidebar
  return (
    <Box
      sx={{
        display: 'flex',
        minHeight: '100svh',
        maxWidth: '100vw',
        overflow: 'hidden',
      }}
    >
      {!isNotFound && <ChatHistorySidebar />}
      <Box
        component="main"
        onClick={handleContentClick}
        sx={{
          flexGrow: 1,
          transition: 'all 0.2s ease-out',
          width: isNotFound ? '100vw' : '100%',
          paddingLeft: isNotFound ? 0 : {
            xs: 0,
            md: isSidebarExpanded
              ? `${SIDEBAR_WIDTH_EXPANDED}px`
              : `${SIDEBAR_WIDTH_COLLAPSED}px`,
          },
        }}
      >
        {!isNotFound && (
          <IconButton
            onClick={toggleSidebar}
            size='small'
            sx={{
              display: { xs: 'block', md: 'none' },
              position: 'absolute',
              height: theme.mixins.toolbar?.minHeight,
              py: 0.5,
              top: 0,
              left: 16,
              zIndex: 1199,
              color: 'text.secondary',
              opacity: isSidebarExpanded ? 0 : 1,
              pointerEvents: isSidebarExpanded ? 'none' : 'auto',
              transition: 'opacity 0.2s ease-in-out',
            }}
          >
            <MenuIcon color='action' />
          </IconButton>
        )}
        {children}
      </Box>
    </Box>
  );
}

function AppContent({ children }: { children: React.ReactNode }) {
  const { status } = useSession();
  const router = useRouter();
  const pathname = usePathname();
  const { isNotFound } = useChatLayout();

  const initPath = '/';
  const backgroundImagePath = '/image.jpg';
  const signInPath = '/api/auth/signin';
  const errorPath = '/api/auth/error';
  const apiAuthPrefix = '/api/auth';

  const isPublicPath =
    pathname === backgroundImagePath ||
    pathname === initPath ||
    pathname === signInPath ||
    pathname === errorPath ||
    pathname.startsWith(apiAuthPrefix);

  useEffect(() => {
    if (status === 'loading') return;

    if (status === 'unauthenticated' && !isPublicPath) {
      router.push('/');
    }
  }, [status, router, pathname, isPublicPath]);

  if (status === 'loading') {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100svh',
        }}
      >
        Loading...
      </Box>
    );
  }

  if (status === 'unauthenticated' && isPublicPath) {
    return <>{children}</>;
  }

  if (status === 'authenticated') {
    // Wrap the authenticated layout in Suspense for search params
    return (
      <Suspense
        fallback={
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100svh',
            }}
          >
            Loading...
          </Box>
        }
      >
        <FullscreenHandler>
          <AuthenticatedLayout isNotFound={isNotFound}>
            {children}
          </AuthenticatedLayout>
        </FullscreenHandler>
      </Suspense>
    );
  }

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100svh',
      }}
    >
      Redirecting to sign-in...
    </Box>
  );
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <title>HKBU GenAI Platform</title>
        <meta name="description" content="HKBU Generative AI Platform" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0" />
      </head>
      <body id="__next" className="antialiased">
        <StoreProvider>
          <Suspense fallback={null}>
            <GoogleAnalytics />
          </Suspense>
          <ThemeRegistry>
            <AuthProvider>
              <ConfigProvider>
                <ChatLayoutProvider>
                  <AppContent>{children}</AppContent>
                </ChatLayoutProvider>
              </ConfigProvider>
            </AuthProvider>
          </ThemeRegistry>
        </StoreProvider>
      </body>
    </html>
  );
}
