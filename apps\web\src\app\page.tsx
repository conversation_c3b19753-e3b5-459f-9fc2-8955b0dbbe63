'use client'; // Required for client-side interactions

import React, {
  useMemo,
  useState,
  useEffect,
  useRef,
  useCallback,
} from 'react'; // Import useMemo, useState, useEffect, useRef
import { useRouter } from 'next/navigation'; // Keep this import
import { useDispatch } from 'react-redux'; // Import useDispatch
import Typography from '@mui/material/Typography';
import Container from '@mui/material/Container';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid'; // Keep Grid for potential section layout
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import SearchIcon from '@mui/icons-material/Search';
import IconButton from '@mui/material/IconButton';
// import SettingsIcon from '@mui/icons-material/Settings'; // No longer needed here
import Divider from '@mui/material/Divider';
import CircularProgress from '@mui/material/CircularProgress'; // For loading state
import Alert from '@mui/material/Alert'; // For error state
import Chip from '@mui/material/Chip'; // Import Chip for recent models
// SerializedError import moved to ModelListSection.tsx
import { toast, ToastContainer } from 'react-toastify'; // Import toast
import 'react-toastify/dist/ReactToastify.css'; // Import toast CSS
import { event } from '@/components/genai/GoogleAnalytics';
import { stripSelectedModelMentions } from '@/lib/utils/mentionUtils'; // Import mention utility
// Placeholder component import - will create later if needed
// import ModelListItem from '@/components/ModelListItem';
import ModelListSection from '@/components/genai/ModelListSection'; // Import the new component
import ModelSearchModal from '@/components/genai/ModelSearchModal'; // Import the new modal component
import ChatInputArea from '@/components/genai/chat/ChatInputArea'; // Import ChatInputArea
import PromptGalleryModal from '@/components/genai/modals/PromptGalleryModal'; // Import PromptGalleryModal
import {
  useGetModelsQuery,
  useGetRecentModelsQuery,
} from '@/lib/store/apiSlice'; // Import RTK Query hooks
import { useSelector } from 'react-redux'; // Import useSelector
import { signIn, useSession } from 'next-auth/react'; // Import useSession
import { selectIsAuthenticated } from '@/lib/store/authSlice'; // Import selectors
import { selectAvailableModels } from '@/lib/store/modelSlice'; // Import model selectors
import { GptModel } from '@/lib/types/common'; // Import GptModel type
import { modelInfo } from '@/components/genai/model/ModelInfo'; // Import modelInfo for icons
import { triggerClearMessage, messageCleared, selectSelectedNextModelName, setSelectedNextModelName } from '@/lib/store/chatSlice'; // Import the actions and selectors
import OpenAILogo from '@/assets/icons/OpenAILogo';
import DeepSeekLogo from '@/assets/icons/DeepSeekLogo';
import { head } from 'lodash';
import HkbuLogo from '@/assets/icons/HkbuLogo';
import ForumOutlinedIcon from '@mui/icons-material/ForumOutlined';
import { Button, Link } from '@mui/material';
import ArrowButton, { Direction } from '@/components/genai/ArrowButton';
import WelcomePage from '@/components/genai/WelcomePage';

// Use GptModel type imported from common types
// interface Model { ... }

// Placeholder data removed - will use data from RTK Query hooks
// const recentlyUsedModels = [ ... ];
// const generalModels = [ ... ];
// const thinkingModels = [ ... ];
// const imageModels = [ ... ];

// Define specific thinking model names based on the initial request
const THINKING_MODEL_NAMES = [
  'deepseek-r1',
  'o1-preview', // Assuming o1 maps to o1-preview
  'o1-mini',
  'o3-mini',
  'gemini-2.5-pro', // Assuming this exists or will be added
  'gemini-2.0-flash-thinking', // Example if a specific variant exists
];

// Define specific image model names based on the initial request
const IMAGE_MODEL_NAMES = ['Adobe Express', 'Adobe Firefly'];

// Removed inline ModelListSection component definition and its type alias

// Hardcoded Adobe models (since they don't come from the API)
const ADOBE_EXPRESS_MODEL: GptModel = {
  display_name: 'Adobe Express',
  deployment_name: 'adobe-express', // Placeholder deployment name
  model_name: 'adobe-express', // Placeholder model name
  category: 'image generation',
};

const ADOBE_FIREFLY_MODEL: GptModel = {
  display_name: 'Adobe Firefly',
  deployment_name: 'adobe-firefly', // Placeholder deployment name
  model_name: 'adobe-firefly', // Placeholder model name
  category: 'image generation',
};

const HARDCODED_IMAGE_MODELS = [ADOBE_EXPRESS_MODEL, ADOBE_FIREFLY_MODEL];

export default function RootPage() {
  const { status } = useSession();

  switch (status) {
    case 'authenticated':
      return <NewModelSelectionPage />;
    case 'loading':
      return <></>;
    case 'unauthenticated':
      return <WelcomePage />;
  }
}

function NewModelSelectionPage() {
  const router = useRouter();
  const dispatch = useDispatch(); // Get dispatch function
  
  // Redux selectors
  const availableModels = useSelector(selectAvailableModels); // Get available models from Redux
  const selectedNextModelName = useSelector(selectSelectedNextModelName); // Get selected model from @mention
  const isAuthenticated = useSelector(selectIsAuthenticated); // Get authentication status
  const { data: session } = useSession(); // Get session data
  const accessToken = session?.accessToken; // Get access token from session
  
  // Fetch recent models for auto-reselection
  const { data: recentModels = [], isLoading: isLoadingRecentModels } = useGetRecentModelsQuery(
    { limit: 3 },
    { skip: !isAuthenticated || !accessToken, refetchOnMountOrArgChange: true }
  );
  
  // Fetch all models for ModelSelectionBar
  const { data: allModels = [], isLoading: isLoadingAllModels } = useGetModelsQuery(
    undefined,
    { skip: !isAuthenticated || !accessToken }
  );
  const [selectedFeaturedModel, setSelectedFeaturedModel] =
    useState<GptModel | null>(null); // State for selected featured model
  const [mentionModelSelected, setMentionModelSelected] = useState(false); // Track explicit @mention usage
  const [selectedMentions, setSelectedMentions] = useState<Map<string, number[]>>(new Map()); // Track @mention selections
  const [isSearchModalOpen, setIsSearchModalOpen] = useState(false); // State for search modal
  const [isPromptGalleryModalOpen, setIsPromptGalleryModalOpen] = useState(false); // State for prompt gallery modal
  // const [homepageQuery, setHomepageQuery] = useState(''); // State for query now managed internally by ChatInputArea via ref
  const [useGoogle, setUseGoogle] = useState(false); // State for Google Search toggle
  const isModelButtonClickedRef = useRef(false); // Ref to track if user is actively clicking model buttons (using ref for immediate updates)
  const homepageInputRef = useRef<HTMLTextAreaElement>(null); // Ref for ChatInputArea
  const clearSelectedMentionsRef = useRef<(() => void) | undefined>(undefined); // Ref to clear @mention selections

  // Clear chat state when homepage mounts to ensure fresh start
  useEffect(() => {
    dispatch(triggerClearMessage());
    dispatch(messageCleared());
  }, [dispatch]);

  // Unclick featured model when @mention model is explicitly selected
  useEffect(() => {
    // Don't unclick featured model if user is actively clicking model buttons
    if (isModelButtonClickedRef.current) {
      console.log('Skipping @mention unclick due to model button click in progress');
      return;
    }
    
    if (mentionModelSelected && selectedFeaturedModel) {
      // If user explicitly selected a model via @mention, clear the featured model selection
      setSelectedFeaturedModel(null);
      setMentionModelSelected(false); // Reset flag after handling
      console.log(`Unclicked featured model due to explicit @mention selection: ${selectedNextModelName}`);
    }
  }, [mentionModelSelected, selectedFeaturedModel, selectedNextModelName]);

  // Updated function to accept the full model object and navigate
  const handleSelectModel = (model: GptModel) => {
    // Check if model_name is valid before navigating
    if (
      model &&
      typeof model.model_name === 'string' &&
      model.model_name.trim() !== ''
    ) {
      // Handle Adobe Express and Firefly models by opening their external URLs
      if (model.model_name === 'adobe-express') {
        window.open('https://new.express.adobe.com/', '_blank');
        event({
          action: 'click',
          category: 'homepage',
          label: 'select_adobe_express',
          value: 1,
        });
        return;
      }
      
      if (model.model_name === 'adobe-firefly') {
        window.open('https://firefly.adobe.com/', '_blank');
        event({
          action: 'click',
          category: 'homepage',
          label: 'select_adobe_firefly',
          value: 1,
        });
        return;
      }

      // Clear chat state BEFORE navigating - trigger and complete the clear
      dispatch(triggerClearMessage());
      dispatch(messageCleared()); // Immediately clear the state
      // TODO: Call POST /recent-models/usage API (Consider doing this on the chat page or via a dedicated action)
      // TODO: Set selected model in Redux state (Consider doing this on the chat page or via a dedicated action)
      router.push(`/chat/new?model=${encodeURIComponent(model.model_name)}`); // Pass model_name
      event({
        action: 'click',
        category: 'homepage',
        label: 'select_model_from_list',
        value: 1,
      });
    } else {
      // Update error messages to reflect checking model_name
      console.error(
        `Cannot navigate: Model "${model?.display_name}" has missing or invalid model_name:`,
        model?.model_name,
      );
      toast.error(
        `Cannot start chat: Model "${model?.display_name}" configuration is incomplete (missing model name).`,
      );
    }
  };

  // Handler for explicit user clicks on featured model chips
  const handleExplicitModelSelection = useCallback(
    (model: GptModel) => {
      console.log(`[EXPLICIT CLICK] User clicked model button: ${model.display_name}, currently selected: ${selectedFeaturedModel?.display_name || 'none'}`);
      
      // Set flag to prevent auto-reselection when clearing mentions
      isModelButtonClickedRef.current = true;
      
      // Helper function to strip @mention text from input field
      const stripMentionFromInput = () => {
        if (homepageInputRef.current && selectedMentions.size > 0) {
          const currentValue = homepageInputRef.current.value;
          const strippedValue = stripSelectedModelMentions(currentValue, selectedMentions, availableModels);
          
          // Update input field with stripped value
          const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
            homepageInputRef.current instanceof HTMLInputElement
              ? window.HTMLInputElement.prototype
              : window.HTMLTextAreaElement.prototype,
            'value',
          )?.set;
          nativeInputValueSetter?.call(homepageInputRef.current, strippedValue);
          
          // Dispatch input event to trigger any listeners
          const inputEvent = new Event('input', { bubbles: true });
          homepageInputRef.current.dispatchEvent(inputEvent);
          
          console.log(`Stripped @mention from input: "${currentValue}" → "${strippedValue}"`);
        }
      };

      // If clicking the same model again, deselect it
      if (selectedFeaturedModel?.model_name === model.model_name) {
        console.log('[EXPLICIT CLICK] Deselecting same model:', model.display_name);
        setSelectedFeaturedModel(null);
        // Clear @mention text and Redux state when deselecting
        dispatch(setSelectedNextModelName(undefined));
        stripMentionFromInput(); // Strip @mention text from input field
        
        // Use setTimeout to clear mentions after state updates complete
        setTimeout(() => {
          clearSelectedMentionsRef.current?.();
        }, 0);
        
        console.log('[EXPLICIT CLICK] Deselected featured model and cleared @mention:', model.display_name);
      } else {
        console.log('[EXPLICIT CLICK] Selecting new model:', model.display_name);
        
        // FIRST: Set the model selection state immediately
        setSelectedFeaturedModel(model);
        
        // SECOND: Clear the mentionModelSelected flag to prevent conflicts
        setMentionModelSelected(false);
        
        // THIRD: Clear Redux state for @mention
        dispatch(setSelectedNextModelName(undefined));
        
        // FOURTH: Strip @mention text from input
        stripMentionFromInput();
        
        // FIFTH: Clear mention tracking after other operations complete
        // This prevents the auto-reselection from interfering
        setTimeout(() => {
          if (clearSelectedMentionsRef.current) {
            clearSelectedMentionsRef.current();
            console.log('[EXPLICIT CLICK] Cleared mention tracking after model selection');
          }
        }, 0);
        
        console.log('[EXPLICIT CLICK] Selected featured model and cleared @mention:', model.display_name);
        
        // Optionally focus the input field when a model is selected
        homepageInputRef.current?.focus();
        event({
        action: 'click',
        category: 'homepage',
        label: 'select_featured_model',
        value: 1,
      });
    }
    },
    [selectedFeaturedModel, dispatch, selectedMentions, availableModels],
  );

  // Handler for auto-selection (used by ModelSelectionBar for initial setup)
  const handleAutoModelSelection = useCallback(
    (model: GptModel) => {
      console.log(`[AUTO-SELECT] Auto-selecting model: ${model.display_name}`);
      // Only set the model, don't clear mentions or trigger other logic
      setSelectedFeaturedModel(model);
    },
    [],
  );

  // Reset the model button clicked flag after a delay when it's set
  useEffect(() => {
    if (isModelButtonClickedRef.current) {
      const timeout = setTimeout(() => {
        console.log('Resetting isModelButtonClicked flag');
        isModelButtonClickedRef.current = false;
      }, 500); // Increased delay to ensure all state updates and DOM events complete
      
      return () => clearTimeout(timeout);
    }
  });

  // Handlers for search modal
  const handleOpenSearchModal = () => {
    setIsSearchModalOpen(true);
    event({
      action: 'click',
      category: 'homepage',
      label: 'open_search_modal',
      value: 1,
    });
  };
  const handleCloseSearchModal = () => setIsSearchModalOpen(false);

  // Handlers for prompt gallery modal
  const handleOpenPromptGallery = () => setIsPromptGalleryModalOpen(true);
  const handleClosePromptGallery = () => setIsPromptGalleryModalOpen(false);
  
  // Handler for @mention model selection
  const handleMentionModelSelected = useCallback(() => {
    setMentionModelSelected(true);
    console.log('User explicitly selected a model via @mention');
  }, []);
  
  // Handler for tracking @mention selections
  const handleSelectedMentionsChange = useCallback((mentions: Map<string, number[]>) => {
    setSelectedMentions(mentions);
  }, []);
  
  // Monitor for manual @mention removal and auto-reselect recent model
  useEffect(() => {
    const inputElement = homepageInputRef.current;
    if (!inputElement) return;
    
    const handleInputChange = () => {
      // Prevent any auto-reselection if user is actively clicking model buttons
      if (isModelButtonClickedRef.current) {
        console.log('Skipping auto-reselection: model button click in progress');
        return;
      }
      
      const inputValue = inputElement.value || '';
      const hasMentionText = /@\w+/.test(inputValue);
      const hasMentionTracking = selectedMentions.size > 0;
      
      // If we had @mention tracking but no @mention text exists, user manually removed it
      // Only auto-reselect if user is NOT actively clicking model buttons AND not during @mention selection
      if (!hasMentionText && hasMentionTracking && !mentionModelSelected && !isModelButtonClickedRef.current) {
        console.log('Detected manual @mention removal, auto-selecting recent model');
        setSelectedMentions(new Map()); // Clear tracking
        dispatch(setSelectedNextModelName(undefined)); // Clear Redux state
        
        // Auto-reselect the first recent model only if no model is currently selected
        if (recentModels.length > 0 && !selectedFeaturedModel) {
          setSelectedFeaturedModel(recentModels[0]);
          console.log('Auto-selected recent model:', recentModels[0].display_name);
        }
      }
    };
    
    // Listen for input changes
    inputElement.addEventListener('input', handleInputChange);
    
    return () => {
      inputElement.removeEventListener('input', handleInputChange);
    };
  }, [selectedMentions, mentionModelSelected, recentModels, dispatch, selectedFeaturedModel]);
  
  // Handler for prompt selection from gallery
  const handlePromptSelectFromGallery = useCallback((prompt: { prompt_content: string; system_instruction: string }) => {
    // Store prompt data in sessionStorage for the new chat page
    sessionStorage.setItem('promptContent', prompt.prompt_content);
    sessionStorage.setItem('systemInstruction', prompt.system_instruction);
    
    // Navigate to chat/new with the selected model (if any)
    let newChatUrl = '/chat/new';
    if (selectedFeaturedModel) {
      newChatUrl += `?model=${encodeURIComponent(selectedFeaturedModel.model_name)}`;
    }
    router.push(newChatUrl);
    setIsPromptGalleryModalOpen(false);
  }, [selectedFeaturedModel, router]);

  // Placeholder for search input state and handler - REMOVED as search is now in modal
  // const [searchTerm, setSearchTerm] = React.useState('');
  // const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
  //   setSearchTerm(event.target.value);
  //   // TODO: Implement search/filtering logic
  // }; // Also comment out or remove the closing brace

  // Handle homepage query input change - No longer needed directly, ChatInputArea handles its state

  // Handle submitting the homepage query (called by ChatInputArea's internal submit)
  const handleSubmitForHomepage = () => {
    const rawQuery = homepageInputRef.current?.value?.trim();
    if (rawQuery) {
      // Strip @mention text from the query before submitting
      const query = stripSelectedModelMentions(rawQuery, selectedMentions, availableModels);
      
      // Priority: @mention selection > featured model selection
      let modelToUse: GptModel | null = null;
      
      // First, check if user selected a model via @mention
      if (selectedNextModelName) {
        modelToUse = availableModels.find(m => m.model_name === selectedNextModelName) || null;
        console.log(`Found @mention model: ${modelToUse?.display_name}`);
      }
      
      // If no @mention model, fall back to featured model selection
      if (!modelToUse && selectedFeaturedModel) {
        modelToUse = selectedFeaturedModel;
        console.log(`Using featured model: ${modelToUse.display_name}`);
      }
      
      // If no model is selected at all, show warning
      if (!modelToUse) {
        toast.warning(
          'Please select a featured model, use @mention to select a model, or use the search icon to find a model first.',
        );
        return;
      }
      
      // Check if @mention was used for this model
      const usedMention = selectedMentions.has(modelToUse.model_name);
      
      // Proceed with navigation if model is selected
      console.log(
        `Submitting query "${query}" (stripped from "${rawQuery}") with model ${modelToUse.display_name}, usedMention: ${usedMention}`,
      );
      
      // Build navigation URL with usedMention parameter if applicable
      let navigationUrl = `/chat/new?model=${encodeURIComponent(modelToUse.model_name)}&query=${encodeURIComponent(query)}`;
      if (usedMention) {
        navigationUrl += '&usedMention=1';
      }
      
      router.push(navigationUrl);
      event({
        action: 'click',
        category: 'homepage',
        label: 'submit_query',
        value: 1,
      });
      // Optionally clear input after submit
      if (homepageInputRef.current) homepageInputRef.current.value = '';
    }
  };

  // Dummy/Adapted handlers for ChatInputArea props
  const handleHomepageReset = () => {
    if (homepageInputRef.current) homepageInputRef.current.value = '';
    // Reset other relevant states if needed
  };
  const setHomepageChatError = (msg: string | undefined) => {
    if (msg) toast.error(msg);
  };
  const dummySetFileList = () => {}; // Dummy function
  const dummyOnTextChange = () => {}; // Dummy function
  const dummyOnClickStop = () => {}; // Dummy function

  // Log auth state before calling useGetModelsQuery
  console.log(
    '[DEBUG] NewModelSelectionPage - isAuthenticated:',
    isAuthenticated,
    'accessToken:',
    accessToken ? `Present (length: ${accessToken.length})` : 'Not present',
  );

  // Moved useGetModelsQuery and related logic to AuthenticatedModelLists sub-component
  // const { data: allModels = [], isLoading: isLoadingAllModels, error: errorAllModels, refetch: refetchGetModels, isUninitialized } = useGetModelsQuery(
  //   undefined, // No argument for getModels
  //   { skip: !isAuthenticated || !accessToken } // Skip if not authenticated OR no token
  // );

  // useEffect(() => {
  //   if (isAuthenticated && accessToken && (isUninitialized || errorAllModels)) {
  //     // If authenticated, token is present, and query is uninitialized (was skipped) or previously errored (e.g. 401)
  //     console.log('[DEBUG] NewModelSelectionPage - Attempting to refetch getModels.');
  //     refetchGetModels();
  //   }
  // }, [isAuthenticated, accessToken, refetchGetModels, isUninitialized, errorAllModels]);

  return (
    // Close Fragment
    <>
      <ToastContainer position="top-right" autoClose={5000} theme="colored" />
      <Box
        sx={{ display: 'flex', minHeight: '100svh', justifyContent: 'center' }}
      >
        {' '}
        {/* Main flex container */}
        {/* Main content area takes remaining space */}
        {/* Add transition to main content area if needed for smooth sidebar collapse */}
        {/* Main content area takes remaining space - Added dynamic marginLeft */}
        <Box
          // component="main"
          sx={{
            display: 'flex',
            flexDirection: 'column',
            minWidth: { xs: 0, sm: 800 },
            width: { xs: '100%', sm: '70%' },
            px: { xs: '20px', sm: 0 },
            bgcolor: 'background.default',
            py: 10, // Keep padding 0 if Container handles it
            transition: 'margin-left 0.2s ease-out', // Match sidebar transition
            marginLeft: 0, // Reset margin
          }}
        >
          <PageHeader />
          {/* 2) Featured Model Chips & Search Icon */}
          {isAuthenticated && (
            <ModelSelectionBar
              selectedFeaturedModel={selectedFeaturedModel}
              handleExplicitModelSelection={handleExplicitModelSelection}
              handleAutoModelSelection={handleAutoModelSelection}
              handleOpenSearchModal={handleOpenSearchModal}
              selectedNextModelName={selectedNextModelName}
              isModelButtonClicked={isModelButtonClickedRef.current}
              recentModels={recentModels}
              allModels={allModels}
              isLoadingRecentModels={isLoadingRecentModels}
              isLoadingAllModels={isLoadingAllModels}
            />
          )}
          {/* 4) Homepage Chat Input Area */}
          <Box
            sx={{
              alignSelf: 'center',
              mb: 5,
              width: { xs: '100%', sm: '70%' },
            }}
          >
            <ChatInputArea
              isLoading={false} // Not loading on homepage initially
              tempFileList={[]} // No files initially
              setTempFileList={dummySetFileList} // Dummy handler
              handleReset={handleHomepageReset} // Use adapted reset
              handleSubmit={handleSubmitForHomepage} // Use adapted submit
              onTextChange={dummyOnTextChange} // Dummy handler
              onKeyDown={() => {}} // Pass empty handler, submit logic is in handleSubmitForHomepage
              supportedFileList="" // No file support needed here
              visionModelList={[]} // No vision models relevant here
              disableFileUpload={true} // Disable file upload on homepage
              onClickStopButton={dummyOnClickStop} // Dummy handler
              uploadFileSizeLimit={10000000} // Default limit
              sizeExceedsMessage="File size exceeds limit." // Default message
              setChatErrorMessage={setHomepageChatError} // Use adapted error handler
              messageInputRef={
                homepageInputRef as React.RefObject<HTMLTextAreaElement>
              } // Pass the ref
              useGoogleEnabled={useGoogle} // Pass state
              setUseGoogleEnabled={setUseGoogle} // Pass state setter
              hideModelSelector={false} // Enable the model selector on the homepage
              onMentionModelSelected={handleMentionModelSelected} // Handle explicit @mention selection
              onSelectedMentionsChange={handleSelectedMentionsChange} // Track @mention selections
              clearSelectedMentionsRef={clearSelectedMentionsRef} // Ref to clear @mention selections
              onPromptSelect={handleOpenPromptGallery} // Add prompt gallery handler
            />
          </Box>
          {/* Divider removed as recent models are now pills */}
          {/* <Divider sx={{ my: 4 }} /> */}
          {/* Conditionally render AuthenticatedModelLists */}
          {isAuthenticated && accessToken ? (
            <AuthenticatedModelLists onSelectModel={handleSelectModel} />
          ) : (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                py: 4,
                alignItems: 'center',
                height: '200px',
              }}
            >
              <CircularProgress />
              <Typography sx={{ ml: 2 }}>Loading models...</Typography>
            </Box>
          )}
        </Box>
        {/* Render the Search Modal */}
        {/* Settings Icon Button - REMOVED as sidebar toggle is now global */}
      </Box>
      {isAuthenticated && (
        <>
          <ModelSearchModal
            open={isSearchModalOpen}
            onClose={handleCloseSearchModal}
          />
          <PromptGalleryModal
            open={isPromptGalleryModalOpen}
            handleClose={handleClosePromptGallery}
            handlePromptSelect={handlePromptSelectFromGallery}
            isExistingChat={false}
          />
        </>
      )}
    </>
  );
}

// New sub-component to handle model fetching and display once authenticated
const AuthenticatedModelLists: React.FC<{
  onSelectModel: (model: GptModel) => void;
}> = ({ onSelectModel }) => {
  const isAuthenticated = useSelector(selectIsAuthenticated); // Also select here for logging
  const { data: session } = useSession(); // Get session data
  const accessToken = session?.accessToken; // Get access token from session
  const router = useRouter(); // Move useRouter to top, before any conditional returns

  const {
    data: allModels = [],
    isLoading: isLoadingAllModels,
    error: errorAllModels,
  } = useGetModelsQuery(
    undefined,
    { skip: !isAuthenticated || !accessToken }, // Re-add skip condition here for defense in depth
  );

  // Add the recent models query
  const {
    data: recentModels = [],
    isLoading: isLoadingRecentModels,
    error: errorRecentModels,
  } = useGetRecentModelsQuery(
    { limit: 10 }, // Fetch up to 10 recent models
    {
      skip: !isAuthenticated || !accessToken,
      refetchOnMountOrArgChange: true, // Force fresh data on component mount
    },
  );

  // Add useEffect to log recent models when they're loaded
  useEffect(() => {
    if (errorRecentModels) {
      console.error('=== Error Loading Recent Models ===');
      console.error('Error details:', errorRecentModels);
      console.error('===================================');
    } else if (!isLoadingRecentModels && recentModels.length > 0) {
      console.log('=== Recently Used Models ===');
      console.log(`Total recent models: ${recentModels.length}`);
      recentModels.forEach((model, index) => {
        console.log(
          `${index + 1}. ${model.display_name} (${model.model_name})`,
        );
        console.log(`   Category: ${model.category || 'N/A'}`);
        console.log(`   Deployment: ${model.deployment_name || 'N/A'}`);
      });
      console.log('=========================');
    } else if (!isLoadingRecentModels && recentModels.length === 0) {
      console.log('=== Recent Models Query Result ===');
      console.log('No recently used models found for this user.');
      console.log(
        `Query status: Loading=${isLoadingRecentModels}, Error=${!!errorRecentModels}`,
      );
      console.log(
        `Authentication: isAuthenticated=${isAuthenticated}, hasAccessToken=${!!accessToken}`,
      );
      console.log('==================================');
    }
  }, [
    recentModels,
    isLoadingRecentModels,
    errorRecentModels,
    isAuthenticated,
    accessToken,
  ]);

  // Filter categorized models based on the *allModels* list - Use lowercase for comparison
  const generalModels = useMemo(
    () =>
      allModels.filter(
        (
          m: GptModel, // Use allModels, add type
        ) =>
          m.category === 'general' || // Use lowercase
          // Fallback: If category is null/undefined, exclude known thinking/image models
          (m.category == null &&
            !THINKING_MODEL_NAMES.includes(m.model_name ?? '') &&
            !IMAGE_MODEL_NAMES.includes(m.display_name ?? '')), // Added nullish coalescing
      ),
    [allModels],
  ); // Depend on allModels

  const thinkingModels = useMemo(
    () =>
      allModels.filter(
        (
          m: GptModel, // Use allModels, add type
        ) =>
          m.category === 'thinking' || // Use lowercase
          (m.category == null &&
            THINKING_MODEL_NAMES.includes(m.model_name ?? '')), // Fallback using names, added nullish coalescing
      ),
    [allModels],
  ); // Depend on allModels

  const imageModels = useMemo(() => {
    // Filter models from API based on category
    const apiImageModels = allModels.filter(
      (m: GptModel) => m.category === 'image generation',
    ); // Add type

    // Hardcoded models are always included unless filtered within the modal later
    const hardcodedFiltered = HARDCODED_IMAGE_MODELS;

    // Combine the lists (ensure no duplicates if API ever includes them)
    const combined = [...apiImageModels];
    hardcodedFiltered.forEach((hcModel) => {
      if (
        !combined.some(
          (apiModel) => apiModel.display_name === hcModel.display_name,
        )
      ) {
        combined.push(hcModel);
      }
    });

    return combined;
  }, [allModels]);

  if (isLoadingAllModels) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          py: 4,
          alignItems: 'center',
          height: '200px',
        }}
      >
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Loading models...</Typography>
      </Box>
    );
  }

  if (errorAllModels) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <Alert severity="error">
          Failed to load models. Please try again later.
        </Alert>
      </Box>
    );
  }

  return (
    <Box display={'flex'} flexDirection={'column'} gap={3}>
      <ModelListSection
        title="General Model"
        models={generalModels}
        onSelectModel={onSelectModel}
        isLoading={false} // isLoading is handled above
        error={undefined} // error is handled above
        categorySlug="general"
      />
      <ModelListSection
        title="Thinking Model"
        models={thinkingModels}
        onSelectModel={onSelectModel}
        isLoading={false}
        error={undefined}
        categorySlug="thinking"
      />
      <ModelListSection
        title="Image Generation"
        models={imageModels}
        onSelectModel={onSelectModel}
        isLoading={false}
        error={undefined}
        categorySlug="image-generation"
      />

      {/* Browse All Models Button */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 6, mb: 4 }}>
        <Button
          variant="contained"
          size="large"
          onClick={() => {
            router.push('/models');
            event({
              action: 'click',
              category: 'homepage',
              label: 'browse_all_models',
              value: 1,
            });
          }}
          startIcon={<SearchIcon />}
          sx={{
            textTransform: 'none',
            backgroundColor: 'primary.main',
            color: 'primary.contrastText',
            px: 4,
            py: 1.5,
            fontSize: '1.1rem',
            fontWeight: 600,
            borderRadius: '30px',
            boxShadow: '0 4px 12px rgba(4, 120, 87, 0.25)',
            transition: 'all 0.3s ease',
            '&:hover': {
              backgroundColor: 'primary.dark',
              transform: 'translateY(-2px)',
              boxShadow: '0 6px 20px rgba(4, 120, 87, 0.35)',
            },
            '&:active': {
              transform: 'translateY(0)',
            },
          }}
        >
          Browse All Models
        </Button>
      </Box>
    </Box>
  );
};

type ModelSelectionBarProps = {
  selectedFeaturedModel: GptModel | null;
  handleExplicitModelSelection: (_: GptModel) => void; // For user clicks
  handleAutoModelSelection: (_: GptModel) => void; // For auto-selection
  handleOpenSearchModal: () => void;
  selectedNextModelName: string | undefined; // Add selectedNextModelName prop
  isModelButtonClicked: boolean; // Add flag to prevent auto-selection during button clicks
  recentModels: GptModel[]; // Pass recent models from parent
  allModels: GptModel[]; // Pass all models from parent
  isLoadingRecentModels: boolean; // Pass loading state from parent
  isLoadingAllModels: boolean; // Pass loading state from parent
};

const ModelSelectionBar: React.FC<ModelSelectionBarProps> = ({
  selectedFeaturedModel,
  handleExplicitModelSelection,
  handleAutoModelSelection,
  handleOpenSearchModal,
  selectedNextModelName,
  isModelButtonClicked,
  recentModels,
  allModels,
  isLoadingRecentModels,
  isLoadingAllModels,
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null); // Ref for the scrollable container
  const hasAutoSelectedRef = useRef(false); // Track if we've auto-selected (using ref to avoid closure issues)
  const handleAutoModelSelectionRef = useRef(handleAutoModelSelection); // Ref to avoid stale closures
  
  // Update the callback ref when it changes
  useEffect(() => {
    handleAutoModelSelectionRef.current = handleAutoModelSelection;
  }, [handleAutoModelSelection]);
  
  // Debug logging for recent models
  useEffect(() => {
    console.log('[MODEL_BAR] Recent models:', recentModels.map(m => `${m.display_name} (${m.model_name})`));
    console.log('[MODEL_BAR] Selected model:', selectedFeaturedModel?.display_name, selectedFeaturedModel?.model_name);
    console.log('[MODEL_BAR] First recent model name for dependency:', recentModels[0]?.model_name);
  }, [recentModels, selectedFeaturedModel]);

  const [scrollInfo, setScrollInfo] = useState<{
    scrollable: boolean;
    position: number;
    isAtStart: boolean;
    isAtEnd: boolean;
  }>({
    scrollable: false,
    position: 0,
    isAtStart: false,
    isAtEnd: false,
  });

  const updateScrollInfo = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } =
        scrollContainerRef.current;
      const maxScroll = scrollWidth - clientWidth;
      const roundedScrollLeft = Math.round(scrollLeft);

      setScrollInfo({
        scrollable: maxScroll > 0,
        position: roundedScrollLeft,
        isAtStart: roundedScrollLeft === 0,
        isAtEnd: maxScroll > 0 && roundedScrollLeft >= maxScroll - 1,
      });
    }
  };

  // useEffect #1: Initial auto-selection when models first load
  useEffect(() => {
    // Don't auto-select if user has already selected via @mention
    if (selectedNextModelName) {
      return;
    }
    
    // Don't auto-select if user is actively clicking model buttons
    if (isModelButtonClicked) {
      console.log('[INITIAL] Skipping auto-selection due to button click in progress');
      return;
    }
    
    // Wait for both queries to finish loading
    if (isLoadingRecentModels || isLoadingAllModels) {
      console.log('[INITIAL] Waiting for models to load...');
      return;
    }
    
    // Only do initial selection if we haven't selected anything yet
    if (selectedFeaturedModel || hasAutoSelectedRef.current) {
      return;
    }
    
    console.log('[INITIAL] Initial selection effect running:', {
      hasRecentModels: recentModels.length > 0,
      hasAllModels: allModels.length > 0,
      hasAutoSelected: hasAutoSelectedRef.current
    });
    
    // Try to select first recent model, fallback to first available model
    if (recentModels.length > 0) {
      const firstRecentModel = recentModels[0];
      console.log('[INITIAL] Auto-selecting first recent model:', firstRecentModel.display_name);
      handleAutoModelSelectionRef.current(firstRecentModel);
      hasAutoSelectedRef.current = true;
    } else if (allModels.length > 0) {
      console.log('[INITIAL] Auto-selecting first available model:', allModels[0].display_name);
      handleAutoModelSelectionRef.current(allModels[0]);
      hasAutoSelectedRef.current = true;
    }
  }, [recentModels.length, allModels.length, isLoadingRecentModels, isLoadingAllModels, selectedNextModelName, isModelButtonClicked, selectedFeaturedModel, hasAutoSelectedRef.current]);

  // useEffect #2: Re-selection when most recent model changes
  useEffect(() => {
    console.log('[RESELECT] Re-selection effect triggered, dependency value:', recentModels[0]?.model_name);
    
    // Skip if no models loaded yet
    if (recentModels.length === 0) {
      console.log('[RESELECT] Skipping: no recent models loaded');
      return;
    }
    
    // Skip if user selected via @mention
    if (selectedNextModelName) {
      console.log('[RESELECT] Skipping: user selected via @mention');
      return;
    }
    
    // Skip if user is clicking buttons
    if (isModelButtonClicked) {
      console.log('[RESELECT] Skipping re-selection due to button click in progress');
      return;
    }
    
    // Skip if no model is currently selected (initial selection will handle this)
    if (!selectedFeaturedModel) {
      console.log('[RESELECT] Skipping: no model currently selected');
      return;
    }
    
    const mostRecentModel = recentModels[0];
    
    console.log('[RESELECT] Re-selection effect running:', {
      mostRecentModel: mostRecentModel?.display_name,
      mostRecentModelName: mostRecentModel?.model_name,
      selectedModel: selectedFeaturedModel?.display_name,
      selectedModelName: selectedFeaturedModel?.model_name,
      needsReselection: selectedFeaturedModel.model_name !== mostRecentModel?.model_name
    });
    
    // Re-select if the most recent model is different from currently selected
    if (selectedFeaturedModel.model_name !== mostRecentModel?.model_name) {
      console.log('[RESELECT] Re-selecting to most recent model:', mostRecentModel.display_name);
      console.log('[RESELECT] Re-selection details:', {
        from: selectedFeaturedModel.model_name,
        to: mostRecentModel.model_name
      });
      handleAutoModelSelectionRef.current(mostRecentModel);
    }
  }, [recentModels[0]?.model_name, selectedFeaturedModel, selectedNextModelName, isModelButtonClicked]);


  // Handle scroll state and event listeners
  useEffect(() => {
    updateScrollInfo(); // Initial check

    const currentRef = scrollContainerRef.current;
    currentRef?.addEventListener('scroll', updateScrollInfo);

    // Also handle window resize
    window.addEventListener('resize', updateScrollInfo);

    return () => {
      currentRef?.removeEventListener('scroll', updateScrollInfo);
      window.removeEventListener('resize', updateScrollInfo);
    };
  }, []); // Empty dependency array - this effect only needs to run once

  const handleScroll = (direction: Direction) => {
    if (scrollContainerRef.current) {
      const { scrollLeft } = scrollContainerRef.current;
      const scrollAmount = 300;
      const currentScroll = scrollLeft;
      const newScroll =
        direction === Direction.LEFT
          ? currentScroll - scrollAmount
          : currentScroll + scrollAmount;
      scrollContainerRef.current.scrollTo({
        left: newScroll,
        behavior: 'smooth',
      });
    }
  };

  return (
    <Box
      display={'flex'}
      flexDirection={'row'}
      justifyContent={'center'}
      position={'relative'}
    >
      <Box
        ref={scrollContainerRef}
        display={'flex'}
        flexDirection={'row'}
        maxWidth={'100%'}
        gap={1.5}
        pb={1}
        mb={3}
        sx={{
          scrollbarWidth: 'none',
          overflowX: 'auto',
        }}
      >
        {(recentModels.length > 0
          ? recentModels
          : allModels.filter((_, index) => index === 0)
        ).map((model: GptModel, index: number) => (
          <Button
            variant="contained"
            key={`${model.model_name}-${index}`}
            startIcon={
              <Box sx={{ '& svg': { width: 32, height: 32 } }}>
                {modelInfo[model.model_name as keyof typeof modelInfo]?.svg ||
                  modelInfo[model.display_name as keyof typeof modelInfo]
                    ?.svg ||
                  null}
              </Box>
            }
            onClick={() => {
              console.log(`[MODEL_BAR] Button clicked: ${model.display_name} (${model.model_name}) at index ${index}`);
              handleExplicitModelSelection(model);
            }}
            color={
              selectedFeaturedModel?.model_name === model.model_name
                ? 'primary'
                : 'info'
            }
            sx={{
              p: 0,
              py: 0,
              px: '23px',
              textTransform: 'none',
              height: '50px',
              borderRadius: '35px',
              padding: '12px 12px',
              fontSize: '14px',
              fontWeight: '500',
              flexShrink: 0,
            }}
          >
            {model.display_name}
          </Button>
        ))}

        <ArrowButton
          disabled={!scrollInfo.scrollable || scrollInfo.isAtStart}
          direction={Direction.LEFT}
          onClick={() => handleScroll(Direction.LEFT)}
        />
        <ArrowButton
          disabled={!scrollInfo.scrollable || scrollInfo.isAtEnd}
          direction={Direction.RIGHT}
          onClick={() => handleScroll(Direction.RIGHT)}
        />
      </Box>
    </Box>
  );
};

const PageHeader: React.FC = () => {
  return (
    <Box sx={{ textAlign: 'center', mb: 4 }}>
      <Typography
        variant="h4"
        component="h1"
        gutterBottom
        sx={{
          mb: 3,
          fontSize: '38px',
          fontWeight: '400',
          textAlign: 'center',
        }}
      >
        What do you want to know?
      </Typography>
    </Box>
  );
};
