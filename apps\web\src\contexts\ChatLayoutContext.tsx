'use client';

import React, {
  createContext,
  useState,
  useContext,
  ReactNode,
  Dispatch,
  SetStateAction,
  useEffect,
} from 'react';
import { useTheme, useMediaQuery } from '@mui/material';
import { usePathname } from 'next/navigation';

interface ChatLayoutContextProps {
  isSourcesPanelOpen: boolean;
  setIsSourcesPanelOpen: Dispatch<SetStateAction<boolean>>;
  toggleSourcesPanel: () => void;
  isSidebarExpanded: boolean;
  setIsSidebarExpanded: Dispatch<SetStateAction<boolean>>;
  toggleSidebar: () => void;
  closeSidebarOnMobile: () => void;
  isNotFound: boolean;
  setIsNotFound: Dispatch<SetStateAction<boolean>>;
  markNotFound?: () => void;
}

const ChatLayoutContext = createContext<ChatLayoutContextProps | undefined>(
  undefined,
);

export const ChatLayoutProvider = ({ children }: { children: ReactNode }) => {
  const [isSourcesPanelOpen, setIsSourcesPanelOpen] = useState(false);
  const [isNotFound, setIsNotFound] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const pathname = usePathname();

  // Default to true on server, let effect handle client-side state
  const [isSidebarExpanded, setIsSidebarExpanded] = useState(true);

  // This effect handles sidebar state on resize and navigation
  useEffect(() => {
    if (isMobile) {
      setIsSidebarExpanded(false);
    } else {
      setIsSidebarExpanded(true);
    }
  }, [isMobile, pathname]);

  const toggleSourcesPanel = () => {
    setIsSourcesPanelOpen((prev) => !prev);
  };

  const toggleSidebar = () => {
    setIsSidebarExpanded((prev) => !prev);
  };

  const closeSidebarOnMobile = () => {
    if (isMobile) {
      setIsSidebarExpanded(false);
    }
  };

  const markNotFound = () => {
    setIsNotFound(true);
  };

  return (
    <ChatLayoutContext.Provider
      value={{
        isSourcesPanelOpen,
        setIsSourcesPanelOpen,
        toggleSourcesPanel,
        isSidebarExpanded,
        setIsSidebarExpanded,
        toggleSidebar,
        closeSidebarOnMobile,
        isNotFound,
        setIsNotFound,
        markNotFound,
      }}
    >
      {children}
    </ChatLayoutContext.Provider>
  );
};

export const useChatLayout = (): ChatLayoutContextProps => {
  const context = useContext(ChatLayoutContext);
  if (context === undefined) {
    throw new Error('useChatLayout must be used within a ChatLayoutProvider');
  }
  return context;
};
