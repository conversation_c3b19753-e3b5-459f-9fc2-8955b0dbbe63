'use client';

import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Typography from '@mui/material/Typography';
import Container from '@mui/material/Container';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import Button from '@mui/material/Button';
import Chip from '@mui/material/Chip';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import SearchIcon from '@mui/icons-material/Search';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import FilterListIcon from '@mui/icons-material/FilterList';
import { useGetModelsQuery } from '@/lib/store/apiSlice';
import { GptModel } from '@/lib/types/common';
import ModelCard from '@/components/genai/ModelCard';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { event } from '@/components/genai/GoogleAnalytics';
import { useMediaQuery, Toolbar, useTheme } from '@mui/material';

// Define specific model names based on category (matching main page logic)
const THINKING_MODEL_NAMES = [
  'deepseek-r1',
  'o1-preview',
  'o1-mini',
  'o3-mini',
  'gemini-2.5-pro',
  'gemini-2.0-flash-thinking',
];

const IMAGE_MODEL_NAMES = ['Adobe Express', 'Adobe Firefly'];

// Hardcoded Adobe models (matching main page)
const ADOBE_EXPRESS_MODEL: GptModel = {
  display_name: 'Adobe Express',
  deployment_name: 'adobe-express',
  model_name: 'adobe-express',
  category: 'image generation',
};

const ADOBE_FIREFLY_MODEL: GptModel = {
  display_name: 'Adobe Firefly',
  deployment_name: 'adobe-firefly',
  model_name: 'adobe-firefly',
  category: 'image generation',
};

const HARDCODED_IMAGE_MODELS = [ADOBE_EXPRESS_MODEL, ADOBE_FIREFLY_MODEL];

// Extract unique companies from model names
const extractCompany = (modelName: string): string => {
  const lowerName = modelName.toLowerCase();
  if (
    lowerName.includes('gpt') ||
    lowerName.includes('o1') ||
    lowerName.includes('o3')
  )
    return 'OpenAI';
  if (lowerName.includes('claude')) return 'Anthropic';
  if (lowerName.includes('gemini')) return 'Google';
  if (lowerName.includes('deepseek')) return 'DeepSeek';
  if (lowerName.includes('llama')) return 'Meta';
  if (lowerName.includes('qwen')) return 'Alibaba';
  if (lowerName.includes('adobe')) return 'Adobe';
  if (lowerName.includes('grok')) return 'xAI';
  // Instead of returning 'Other', return empty string to filter out unknown models
  return '';
};

export default function ModelsPage() {
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedCompanies, setSelectedCompanies] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [expandedSections, setExpandedSections] = useState<string[]>([
    'general',
    'thinking',
    'image-generation',
  ]);

  const { data: allModels = [], isLoading, error } = useGetModelsQuery();

  // Handle URL parameters for pre-selected category
  useEffect(() => {
    const categoryParam = searchParams.get('category');
    if (categoryParam) {
      setSelectedCategories([categoryParam]);
      setShowFilters(true); // Show filters when category is pre-selected
      // Expand only the selected category
      setExpandedSections([categoryParam]);
    }
  }, [searchParams]);

  // Categorize models
  const categorizedModels = useMemo(() => {
    const generalModels = allModels.filter(
      (m: GptModel) =>
        m.category === 'general' ||
        (m.category == null &&
          !THINKING_MODEL_NAMES.includes(m.model_name ?? '') &&
          !IMAGE_MODEL_NAMES.includes(m.display_name ?? '')),
    );

    const thinkingModels = allModels.filter(
      (m: GptModel) =>
        m.category === 'thinking' ||
        (m.category == null &&
          THINKING_MODEL_NAMES.includes(m.model_name ?? '')),
    );

    // Image models with hardcoded ones
    const apiImageModels = allModels.filter(
      (m: GptModel) => m.category === 'image generation',
    );

    const imageModels = [...apiImageModels];
    HARDCODED_IMAGE_MODELS.forEach((hcModel) => {
      if (
        !imageModels.some(
          (apiModel) => apiModel.display_name === hcModel.display_name,
        )
      ) {
        imageModels.push(hcModel);
      }
    });

    return {
      general: generalModels,
      thinking: thinkingModels,
      'image-generation': imageModels,
    };
  }, [allModels]);

  // Get unique companies from all models
  const uniqueCompanies = useMemo(() => {
    const companies = new Set<string>();
    const allModelsList = [...allModels, ...HARDCODED_IMAGE_MODELS];

    allModelsList.forEach((model) => {
      const company = extractCompany(model.model_name || model.display_name);
      if (company) { // Only add non-empty company names
        companies.add(company);
      }
    });

    // Convert to array and filter out empty strings
    const companiesArray = Array.from(companies).filter((company) => company.trim() !== '');

    return companiesArray.sort();
  }, [allModels]);

  // Filter models based on search and filters
  const filteredModels = useMemo(() => {
    const searchLower = searchTerm.toLowerCase();

    const filterBySearch = (model: GptModel) => {
      if (!searchTerm) return true;
      return (
        model.display_name?.toLowerCase().includes(searchLower) ||
        model.model_name?.toLowerCase().includes(searchLower) ||
        model.category?.toLowerCase().includes(searchLower) ||
        extractCompany(model.model_name || model.display_name)
          .toLowerCase()
          .includes(searchLower)
      );
    };

    const filterByCategory = (category: string) => {
      if (selectedCategories.length === 0) return true;
      return selectedCategories.includes(category);
    };

    const filterByCompany = (model: GptModel) => {
      const company = extractCompany(model.model_name || model.display_name);
      // Only show models that have a recognized company (not empty string)
      if (!company) return false;
      // If no companies are selected, show all models with recognized companies
      if (selectedCompanies.length === 0) return true;
      return selectedCompanies.includes(company);
    };

    const result: typeof categorizedModels = {
      general: [],
      thinking: [],
      'image-generation': [],
    };

    Object.entries(categorizedModels).forEach(([category, models]) => {
      if (filterByCategory(category)) {
        result[category as keyof typeof result] = models.filter(
          (model) => filterBySearch(model) && filterByCompany(model),
        );
      }
    });

    return result;
  }, [searchTerm, selectedCategories, selectedCompanies, categorizedModels]);

  // Handle model selection
  const handleSelectModel = useCallback(
    (model: GptModel) => {
      if (
        model &&
        typeof model.model_name === 'string' &&
        model.model_name.trim() !== ''
      ) {
        // Handle Adobe Express and Firefly models by opening their external URLs
        if (model.model_name === 'adobe-express') {
          window.open('https://new.express.adobe.com/', '_blank');
          event({
            action: 'click',
            category: 'models_page',
            label: 'select_adobe_express',
            value: 1,
          });
          return;
        }
        
        if (model.model_name === 'adobe-firefly') {
          window.open('https://firefly.adobe.com/', '_blank');
          event({
            action: 'click',
            category: 'models_page',
            label: 'select_adobe_firefly',
            value: 1,
          });
          return;
        }

        console.log(
          `Selected model: ${model.display_name} (${model.model_name})`,
        );
        router.push(`/chat/new?model=${encodeURIComponent(model.model_name)}`);
        event({
          action: 'click',
          category: 'models_page',
          label: 'select_model',
          value: 1,
        });
      } else {
        console.error(
          `Cannot navigate: Model "${model?.display_name}" has missing or invalid model_name:`,
          model?.model_name,
        );
        toast.error(
          `Cannot start chat: Model "${model?.display_name}" configuration is incomplete (missing model name).`,
        );
      }
    },
    [router],
  );

  // Toggle category filter
  const toggleCategory = (category: string) => {
    setSelectedCategories((prev) =>
      prev.includes(category)
        ? prev.filter((c) => c !== category)
        : [...prev, category],
    );
  };

  // Toggle company filter
  const toggleCompany = (company: string) => {
    setSelectedCompanies((prev) =>
      prev.includes(company)
        ? prev.filter((c) => c !== company)
        : [...prev, company],
    );
  };

  // Toggle section expansion
  const toggleSection = (section: string) => {
    setExpandedSections((prev) =>
      prev.includes(section)
        ? prev.filter((s) => s !== section)
        : [...prev, section],
    );
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCategories([]);
    setSelectedCompanies([]);
  };

  // Get display name for category
  const getCategoryDisplayName = (category: string): string => {
    switch (category) {
      case 'general':
        return 'General Models';
      case 'thinking':
        return 'Thinking Models';
      case 'image-generation':
        return 'Image Generation Models';
      default:
        return 'Models';
    }
  };

  // Calculate total filtered models count
  const totalFilteredCount = Object.values(filteredModels).reduce(
    (sum, models) => sum + models.length,
    0,
  );

  if (isLoading) {
    return (
      <Container sx={{ py: 4 }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '50vh',
          }}
        >
          <CircularProgress />
          <Typography sx={{ ml: 2 }}>Loading models...</Typography>
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container sx={{ py: 4 }}>
        <Alert severity="error">
          Failed to load models. Please try again later.
        </Alert>
      </Container>
    );
  }

  return (
    <>
      <ToastContainer position="top-right" autoClose={5000} theme="colored" />
      <Container sx={{ pb: 4, maxWidth: 'xl' }}>
        {isSmallScreen ? (
          <Toolbar
            variant="dense"
            disableGutters
            sx={{
              justifyContent: 'space-between',
              position: 'relative',
              my: 0.5,
            }}
          >
            <Box sx={{ width: 34, display: { xs: undefined, md: 'none' } }} />
            <Typography
              variant="h6"
              noWrap
              component="div"
              sx={{
                flex: 1,
                textAlign: 'center',
                fontWeight: 'medium',
                opacity: 0.7,
                cursor: 'pointer',
                fontSize: '1rem',
              }}
            >
              All Models
            </Typography>
            <Box sx={{ width: 34, display: { xs: undefined, md: 'none' } }} />
          </Toolbar>
        ) : null}
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          {!isSmallScreen ? (
            <Typography variant="h4" component="h1" gutterBottom pt={4}>
              All Models
            </Typography>
          ) : null}
          <Typography variant="subtitle1" color="text.secondary">
            Browse and search through all available AI models. Click on any
            model to start a conversation.
          </Typography>
        </Box>

        {/* Search and Filters */}
        <Box sx={{ mb: 4 }}>
          {/* Search Bar */}
          <Box
            sx={{
              mb: 2,
              display: 'flex',
              gap: { xs: 1, sm: 2 },
              alignItems: 'center',
              flexDirection: { xs: 'column', sm: 'row' },
              width: '100%',
            }}
          >
            <TextField
              fullWidth
              variant="outlined"
              placeholder="Search by model name, category, or company..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                event({
                  action: 'search',
                  category: 'models_page',
                  label: 'search_model',
                  value: 1,
                });
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
            <Box
              sx={{
                display: 'flex',
                gap: 1,
                alignItems: 'center',
                width: { xs: '100%', sm: 'auto' },
                justifyContent: { xs: 'center', sm: 'flex-start' },
              }}
            >
              <Button
                variant="outlined"
                startIcon={<FilterListIcon />}
                onClick={() => {
                  setShowFilters(!showFilters);
                  event({
                    action: 'click',
                    category: 'models_page',
                    label: 'toggle_filters',
                    value: 1,
                  });
                }}
                sx={{
                  minWidth: { xs: 100, sm: 120 },
                  whiteSpace: 'nowrap',
                }}
              >
                Filters{' '}
                {(selectedCategories.length > 0 ||
                  selectedCompanies.length > 0) &&
                  `(${selectedCategories.length + selectedCompanies.length})`}
              </Button>
              {(searchTerm ||
                selectedCategories.length > 0 ||
                selectedCompanies.length > 0) && (
                <Button
                  variant="text"
                  onClick={() => {
                    clearFilters();
                    event({
                      action: 'click',
                      category: 'models_page',
                      label: 'clear_filters',
                      value: 1,
                    });
                  }}
                  sx={{ whiteSpace: 'nowrap' }}
                >
                  Clear All
                </Button>
              )}
            </Box>
          </Box>

          {/* Filter Options */}
          {showFilters && (
            <Box
              sx={{
                p: 2,
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 2,
              }}
            >
              {/* Category Filters */}
              <Box sx={{ mb: 2 }}>
                <Typography
                  variant="subtitle2"
                  gutterBottom
                  sx={{ fontWeight: 600 }}
                >
                  Categories
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {Object.keys(categorizedModels).map((category) => (
                    <Chip
                      key={category}
                      label={getCategoryDisplayName(category)}
                      onClick={() => {
                        toggleCategory(category);
                        event({
                          action: 'click',
                          category: 'models_page',
                          label: `toggle_category_${category}`,
                          value: 1,
                        });
                      }}
                      color={
                        selectedCategories.includes(category)
                          ? 'primary'
                          : 'default'
                      }
                      variant={
                        selectedCategories.includes(category)
                          ? 'filled'
                          : 'outlined'
                      }
                    />
                  ))}
                </Box>
              </Box>

              {/* Company Filters */}
              <Box>
                <Typography
                  variant="subtitle2"
                  gutterBottom
                  sx={{ fontWeight: 600 }}
                >
                  Companies
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {uniqueCompanies.map((company) => (
                    <Chip
                      key={company}
                      label={company}
                      onClick={() => {
                        toggleCompany(company);
                        event({
                          action: 'click',
                          category: 'models_page',
                          label: `toggle_company_${company}`,
                          value: 1,
                        });
                      }}
                      color={
                        selectedCompanies.includes(company)
                          ? 'primary'
                          : 'default'
                      }
                      variant={
                        selectedCompanies.includes(company)
                          ? 'filled'
                          : 'outlined'
                      }
                    />
                  ))}
                </Box>
              </Box>
            </Box>
          )}
        </Box>

        {/* Results Count */}
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Showing {totalFilteredCount} models
          {searchTerm && ` matching "${searchTerm}"`}
        </Typography>

        {/* Model Sections */}
        {totalFilteredCount === 0 ? (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Typography variant="h6" color="text.secondary">
              No models found matching your search criteria.
            </Typography>
            <Button variant="text" onClick={clearFilters} sx={{ mt: 2 }}>
              Clear filters
            </Button>
          </Box>
        ) : (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {Object.entries(filteredModels).map(([category, models]) => {
              if (models.length === 0) return null;

              return (
                <Accordion
                  key={category}
                  expanded={expandedSections.includes(category)}
                  onChange={() => {
                    toggleSection(category);
                    event({
                      action: 'click',
                      category: 'models_page',
                      label: `toggle_section_${category}`,
                      value: 1,
                    });
                  }}
                  sx={{
                    boxShadow: 'none',
                    '&:before': { display: 'none' },
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: '16px !important',
                    overflow: 'hidden',
                    '&.Mui-expanded': { margin: 0 },
                  }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    sx={{
                      backgroundColor: (theme) =>
                        theme.palette.mode === 'dark'
                          ? 'rgba(255, 255, 255, 0.15)'
                          : 'grey.300',
                      '&:hover': {
                        backgroundColor: (theme) =>
                          theme.palette.mode === 'dark'
                            ? 'rgba(255, 255, 255, 0.2)'
                            : 'grey.400',
                      },
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        width: '100%',
                        pr: 2,
                        pl: 1.5,
                      }}
                    >
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        {getCategoryDisplayName(category)}
                      </Typography>
                      <Chip label={`${models.length} models`} size="small" />
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails sx={{ p: 3}}>
                    <Box
                      sx={{
                        display: 'flex',
                        flexWrap: 'wrap',
                        gap: 2,
                        justifyContent: 'flex-start',
                      }}
                    >
                      {models.map((model) => (
                        <Box
                          key={model.deployment_name || model.model_name}
                          sx={{
                            flexShrink: 0,
                          }}
                        >
                          <ModelCard
                            model={model}
                            onSelectModel={handleSelectModel}
                          />
                        </Box>
                      ))}
                    </Box>
                  </AccordionDetails>
                </Accordion>
              );
            })}
          </Box>
        )}
      </Container>
    </>
  );
}
