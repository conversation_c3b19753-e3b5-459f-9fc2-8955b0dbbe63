'use client';

import React, { useState, useMemo, ChangeEvent } from 'react';
import {
  Box,
  Typography,
  Container,
  TextField,
  CircularProgress,
  useTheme,
  InputAdornment,
  Pagination,
  useMediaQuery,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Button,
  Toolbar,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { event } from '@/components/genai/GoogleAnalytics';
import {
  useGetAllConversationHistoryQuery,
  useRenameConversationMutation,
  useDeleteConversationMutation,
} from '@/lib/store/apiSlice';
import { ConversationHistoryItem } from '@/lib/store/apiSlice';
import moment from 'moment';
import Link from 'next/link';
import { modelInfo } from '@/components/genai/model/ModelInfo';
import ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';
import RenameIcon from '@/assets/icons/RenameIcon';
import DeleteIcon from '@/assets/icons/DeleteIcon';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';

const ITEMS_PER_PAGE = 7;

export default function ChatHistoryPage() {
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const [searchText, setSearchText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [editingState, setEditingState] = useState<{
    id: string;
    title: string;
  } | null>(null);
  const {
    data: historyItems,
    isLoading,
    isError,
    isFetching,
  } = useGetAllConversationHistoryQuery();

  const filteredData = useMemo(() => {
    if (!historyItems) return [];
    return historyItems.filter((item) =>
      item.title.toLowerCase().includes(searchText.toLowerCase()),
    );
  }, [historyItems, searchText]);

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    return filteredData.slice(startIndex, startIndex + ITEMS_PER_PAGE);
  }, [filteredData, currentPage]);

  const totalPages = Math.ceil(filteredData.length / ITEMS_PER_PAGE);

  function handlePageChange(_: ChangeEvent<unknown>, value: number) {
    setCurrentPage(value);
    event({
      action: 'click',
      category: 'chat_history',
      label: 'change_page',
      value: value,
    });
  }

  return (
    <Container maxWidth={false} sx={{ bgcolor: 'background.default' }}>
      {isSmallScreen ? (
        <Toolbar
          variant="dense"
          disableGutters
          sx={{
            justifyContent: 'space-between',
            position: 'relative',
            my: 0.5,
          }}
        >
          <Box sx={{ width: 34, display: { xs: undefined, md: 'none' } }} />
          <Typography
            variant="h6"
            noWrap
            component="div"
            sx={{
              flex: 1,
              textAlign: 'center',
              fontWeight: 'medium',
              opacity: 0.7,
              cursor: 'pointer',
              fontSize: '1rem',
            }}
          >
            History
          </Typography>
          <Box sx={{ width: 34, display: { xs: undefined, md: 'none' } }} />
        </Toolbar>
      ) : null}
      <Box
        display={'flex'}
        flexDirection={'column'}
        py={{ xs: 3, md: 5 }}
        px={3}
        gap={4}
        minHeight={'100svh'}
        alignItems="center"
      >
        {!isSmallScreen ? (
          <Typography fontWeight={600} fontSize={25} lineHeight={1}>
            History
          </Typography>
        ) : null}

        <TextField
          variant="outlined"
          value={searchText}
          onChange={(e) => {
            setSearchText(e.target.value);
            setCurrentPage(1); // Reset to first page on search
            event({
              action: 'search',
              category: 'chat_history',
              label: 'search_conversation',
              value: 1,
            });
          }}
          placeholder="Search title"
          sx={{
            width: { xs: '100%', md: '50%' },
            minWidth: { xs: '100%', md: 500 },
            '& .MuiOutlinedInput-root': {
              borderRadius: '25px',
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: 'primary.main',
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: 'primary.main',
              },
            },
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />

        {(isLoading || isFetching) && (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
            <CircularProgress />
          </Box>
        )}
        {isError && (
          <Typography color="error" sx={{ my: 2 }}>
            Failed to load chat history.
          </Typography>
        )}
        {!isLoading && !isError && paginatedData.length === 0 && (
          <Typography sx={{ my: 2 }}>No chat history found.</Typography>
        )}

        {!isLoading && !isError && paginatedData.length > 0 && (
          <Box
            width={{ xs: '100%', md: '80%' }}
            minWidth={{ xs: '100%', md: 500 }}
            display={'flex'}
            flexDirection={'column'}
            flexGrow={1}
            gap={2}
          >
            {paginatedData.map((item) => (
              <HistoryListItem key={item.id} {...item} />
            ))}
          </Box>
        )}

        {totalPages > 1 && (
          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={handlePageChange}
            color="primary"
            disabled={isFetching}
            sx={{ alignSelf: 'center', mt: 4 }}
            showFirstButton={!isSmallScreen}
            showLastButton={!isSmallScreen}
            size={isSmallScreen ? 'small' : 'medium'}
          />
        )}
      </Box>
    </Container>
  );
}

function HistoryListItem(props: ConversationHistoryItem) {
  const ModelIcon = props.model ? modelInfo[props.model]?.svg : null;
  const [deleteConversation] = useDeleteConversationMutation();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const handleDelete = () => {
    setIsDeleteDialogOpen(true);
    event({
      action: 'click',
      category: 'chat_history',
      label: 'delete_conversation_start',
      value: 1,
    });
  };

  const handleConfirmDelete = () => {
    deleteConversation(props.id);
    setIsDeleteDialogOpen(false);
    event({
      action: 'click',
      category: 'chat_history',
      label: 'delete_conversation_confirm',
      value: 1,
    });
  };

  const handleCancelDelete = () => {
    setIsDeleteDialogOpen(false);
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        gap: 2,
        p: 1.5,
        borderBottom: 1,
        borderColor: 'divider',
        '&:hover': {
          backgroundColor: 'action.hover',
        },
      }}
    >
      <Link
        href={`/chat/${props.id}`}
        passHref
        legacyBehavior
        style={{ textDecoration: 'none', flexGrow: 1, display: 'flex' }}
      >
        <Box
          component="a"
          onClick={() => {
            event({
              action: 'click',
              category: 'chat_history',
              label: 'view_conversation',
              value: 1,
            });
          }}
          sx={{
            display: 'flex',
            flexDirection: 'row',
            gap: 2,
            flexGrow: 1,
            cursor: 'pointer',
          }}
        >
          <Box flex={1}>
            <Typography variant="body2" color="text.secondary" fontSize={12}>
              {moment(props.updated_at).format('MMM D YYYY')}
            </Typography>
            <Typography
              variant="h6"
              color="text.primary"
              fontSize={18}
              fontWeight={500}
              sx={{
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                maxWidth: { xs: '250px', sm: '400px', md: '700px' },
              }}
            >
              {props.title}
            </Typography>
          </Box>
        </Box>
      </Link>
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <IconButton size="large" onClick={handleDelete}>
          <DeleteIcon />
        </IconButton>
      </Box>
      <Dialog
        open={isDeleteDialogOpen}
        onClose={handleCancelDelete}
        BackdropProps={{
          sx: {
            backdropFilter: 'blur(10px)',
          },
        }}
        PaperProps={{
          sx: {
            borderRadius: '16px',
            backgroundColor: 'background.paper',
          },
        }}
      >
        <DialogTitle>Delete Conversation</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this conversation? This action
            cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDelete} color="inherit" variant="outlined" sx={{ borderRadius: '16px' }}>Cancel</Button>
          <Button onClick={handleConfirmDelete} color="primary" variant="contained" autoFocus sx={{ borderRadius: '16px' }}>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
